name: Send submodule updates to parent repo

on:
  push:
    branches: 
      - main 
      - prod
      - investment-report

jobs:
  update:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with: 
          repository: NOAH-AI-CO/NoahServer
          token: ${{ secrets.PAT }}
          submodules: true
          ref: dev
          fetch-depth: 

      - name: Pull & update submodules recursively
        run: |
          git config --global url.https://${{ secrets.PAT }}@github.com/.insteadOf https://github.com/
          git submodule update --force --init --remote --recursive

      - name: Commit
        run: |
          git config user.email "<EMAIL>"
          git config user.name "GitHub Actions - update submodules - agent"
          git add --all
          git commit -m "Update submodules - agent" || echo "No changes to commit"
          git push