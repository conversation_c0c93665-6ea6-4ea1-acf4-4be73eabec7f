import re
import logging
import async<PERSON>
import time
from enum import Enum

from datetime import datetime, timed<PERSON><PERSON>
from typing import List, <PERSON>ct, <PERSON><PERSON>, Any, Optional
from pydantic import BaseModel, Field

from agent.explore.schema import WebSearchSubject, WebSearchRegion, SearchEngine
from tools.core.base_tool import BaseTool
from utils.web_search import (BaseSearch, BingSearch, GoogleSerperSearch, GoogleSerpapiSearch)
from utils.finance.financialmodelingprep import FinancialModelinGprep
from utils.finance.mairui import MaiRui
from utils.scholar import PubMedSearchV2


from config import api_config

logger = logging.getLogger(__name__)


class WebSearchSubQueryInputSchema(BaseModel):
    sub_query: str = Field(description='A refined sub-query derived from the original request for focused web searching **in the working language**.')
    keyword: str = Field(description='The keyword **in the working language** for web search engines.')
    keyword_en: str = Field(description='The English keyword for international/global web search engines. It could be same as the keyword when the working language is English.')
    prefer_region: WebSearchRegion = Field(
        default=None,
        description='Indicates whether to prioritize special region search engine. Regardless of the language used in the query, only enable when query contains country or regions. Default is global'
    )


class WebSearchInputSchema(BaseModel):
    thought_process: str = Field(description='The step by step explanation **in the working language** of why triggering this query.')
    sub_queries: List[WebSearchSubQueryInputSchema] = Field(description='An array containing sub-queries and their respective keywords for web search must be less than three. Empty when no need web searching.')
    subject: WebSearchSubject = Field(description='Web search subject, i.e. disease, medicine. So we can use different search engine to get better results.')


class GeneralSearch(BaseTool):
    name: str = 'GeneralSearch'
    description: str = 'Performs a web search to retrieve relevant information based on specified keywords.'
    input_schema: BaseModel = WebSearchInputSchema
    strict: bool = True

    # https://serper.dev/
    global_search_engine: BaseSearch = GoogleSerperSearch(
        api_key=api_config.GOOGLE_SERPER_API_KEY,
        top_k=10)
    
    cn_search_engine: BaseSearch = GoogleSerperSearch(
        api_key=api_config.GOOGLE_SERPER_API_KEY,
        region='cn',
        top_k=10)
    
    jp_search_engine: BaseSearch = GoogleSerperSearch(
        api_key=api_config.GOOGLE_SERPER_API_KEY,
        region='jp',
        top_k=10)
    
    # TODO SA region should be changed, since arab region countries may prefere use native news
    arab_search_engine: BaseSearch = GoogleSerperSearch(
        api_key=api_config.GOOGLE_SERPER_API_KEY,
        region='sa',
        top_k=10)

    def _safe_enum_convert(self, result, key, enum_class, default):
        try:
            value = result.get(key, default.value)
            return enum_class(value)
        except ValueError:
            return default
    
    async def run(self, **kwarg):
        sub_queries = kwarg.get("sub_queries", [])

        res = {
            "function": self.name,
            "params": kwarg,

            # detail result
            "subject": WebSearchSubject.MEDICINE,
            "sub_queries": []
        }

        tasks = [(self._web_search_task(sub_query), sub_query) for sub_query in sub_queries[:3]]

        for task, sub_query in tasks:
            try:
                search_result = await task
                if search_result is not None:
                    
                    region = self._safe_enum_convert(sub_query, 'prefer_region', WebSearchRegion, WebSearchRegion.GLOBAL)

                    res['sub_queries'].append({
                        "sub_query": sub_query['sub_query'],
                        "key_word": sub_query['keyword'],
                        "key_word_en": sub_query['keyword_en'],
                        "region": region,
                        "search_type": SearchEngine.MEDICAL,
                        "search_result": search_result
                    })
                    
            except Exception as exc:
                logger.warn(f"[RawQueryWebSearch] query {sub_query['keyword']} failed", exc)
        yield res

    def _web_search_task(self, sub_query):
        keyword = sub_query.get('keyword', '')
        keyword_en = sub_query.get('keyword_en', '')
        prefer_region = self._safe_enum_convert(sub_query, 'prefer_region', WebSearchRegion, WebSearchRegion.GLOBAL)

        if WebSearchRegion.CHINA == prefer_region:
            task = asyncio.create_task(
                self.cn_search_engine.search(query=keyword)
            )
        elif WebSearchRegion.JAPAN == prefer_region:
            task = asyncio.create_task(
                self.jp_search_engine.search(query=keyword)
            )
        elif WebSearchRegion.ARAB == prefer_region:
            task = asyncio.create_task(
                self.arab_search_engine.search(query=keyword)
            )
        else:
            task = asyncio.create_task(
                self.global_search_engine.search(query=keyword_en)
            )

        return task


class MedicalSearch(GeneralSearch):
    name: str = 'MedicalSearch'
    description: str = 'Performs a medical or biotech search on from authoritative websites to retrieve relevant information based on specified keywords.'
    input_schema: BaseModel = WebSearchInputSchema
    strict: bool = True

    # https://learn.microsoft.com/en-us/bing/search-apis/bing-web-search/reference/market-codes
    global_search_engine: BaseSearch = BingSearch(
        api_key=api_config.BING_SEARCH_SUBSCRIPTION_KEY,
        region='en-US',
        top_k=10)
    
    cn_search_engine: BaseSearch = BingSearch(
        api_key=api_config.BING_SEARCH_SUBSCRIPTION_KEY,
        region='zh-CN',
        top_k=10)
    
    jp_search_engine: BaseSearch = BingSearch(
        api_key=api_config.BING_SEARCH_SUBSCRIPTION_KEY,
        region='ja-JP',
        top_k=10)
    # TODO SA region should be changed, since arab region countries may prefere use native news
    arab_search_engine: BaseSearch = BingSearch(
        api_key=api_config.BING_SEARCH_SUBSCRIPTION_KEY,
        region='SA',
        top_k=10)


class NewsSearch(GeneralSearch):
    name: str = 'NewsSearch'
    description: str = 'Performs a news search to retrieve relevant information based on specified keywords.'
    input_schema: BaseModel = WebSearchInputSchema
    strict: bool = True

    # news search
    global_search_engine: BaseSearch = GoogleSerperSearch(
        api_key=api_config.GOOGLE_SERPER_API_KEY,
        top_k=10)
    
    cn_search_engine: BaseSearch = GoogleSerperSearch(
        api_key=api_config.GOOGLE_SERPER_API_KEY,
        region='cn',
        top_k=10)
    
    jp_search_engine: BaseSearch = GoogleSerperSearch(
        api_key=api_config.GOOGLE_SERPER_API_KEY,
        region='jp',
        top_k=10)
    
    # TODO SA region should be changed, since arab region countries may prefere use native news
    arab_search_engine: BaseSearch = GoogleSerperSearch(
        api_key=api_config.GOOGLE_SERPER_API_KEY,
        region='sa',
        top_k=10)

    def _web_search_task(self, sub_query):
        keyword = sub_query.get('keyword', '')
        keyword_en = sub_query.get('keyword_en', '')
        prefer_region = self._safe_enum_convert(sub_query, 'prefer_region', WebSearchRegion, WebSearchRegion.GLOBAL)

        if WebSearchRegion.CHINA == prefer_region:
            task = asyncio.create_task(
                self.cn_search_engine.news(query=keyword)
            )
        elif WebSearchRegion.JAPAN == prefer_region:
            task = asyncio.create_task(
                self.jp_search_engine.news(query=keyword)
            )
        elif WebSearchRegion.ARAB == prefer_region:
            task = asyncio.create_task(
                self.arab_search_engine.news(query=keyword)
            )
        else:
            task = asyncio.create_task(
                self.global_search_engine.news(query=keyword_en)
            )

        return task


class PatentSearch(GeneralSearch):
    name: str = 'PatentSearch'
    description: str = 'Performs a patent search to retrieve relevant information based on specified keywords.'
    input_schema: BaseModel = WebSearchInputSchema
    strict: bool = True

    # news search
    patent_search_engine: BaseSearch = GoogleSerpapiSearch(
        api_key=api_config.GOOGLE_SERPAPI_API_KEY,
        top_k=10)

    def _web_search_task(self, sub_query):
        keyword_en = sub_query.get('keyword_en', '')
        prefer_region = self._safe_enum_convert(sub_query, 'prefer_region', WebSearchRegion, WebSearchRegion.GLOBAL)
        country = None
        if prefer_region == WebSearchRegion.CHINA:
            country = 'CN'
        elif prefer_region == WebSearchRegion.JAPAN:
            country = 'JP'

        return asyncio.create_task(
            self.patent_search_engine.patents(query=keyword_en, country=country)
        )


class PubmedArticlesSearchInputSchema(BaseModel):
    thought_process: str = Field(description='The step by step  explanation **in the working language** of why triggering this query.')
    pubmed_query: str = Field(description="A PubMed medicine Boolean query, i.e. '(differentiated[Title/Abstract] OR thyroid[Title/Abstract])'. If the value is empty or null, there won't trigger a PubMed search.")


class PubmedArticlesSearch(BaseTool):
    name: str = 'PubmedArticlesSearch'
    description: str = 'Retrieves articles for the given PubMed Boolean query.'
    input_schema: BaseModel = PubmedArticlesSearchInputSchema
    strict: bool = True
    pubmed_client: Optional[PubMedSearchV2] = None    

    def __init__(self, **data):
        super().__init__(**data)
        if self.pubmed_client is None:
            self.pubmed_client = PubMedSearchV2()

    async def run(self, **kwarg):
        #logger.info(f"[Pubmed search] rewrite result is {kwarg}")

        start_time = time.time()

        query = kwarg.get('pubmed_query', '')
        thought_process = kwarg.get('thought_process', '')
        res = {
            "function": self.name,
            "params": kwarg,
            'result': []
        }

        if query == '':
            yield res
            return

        # Call PubMed Entrez search
        try:
            # query PubMed articles
            article_results = await asyncio.wait_for(
                self.pubmed_client.esearch(query=query),
                timeout=30.0  # 20 seconds timeout
            )

            # fetch PubMed articles abstract
            if len(article_results.get('uids', [])) == 0:
                yield res
                return

            ids = ",".join(article_results.get('uids', []))
            abstracts = await asyncio.wait_for(
                self.pubmed_client.efetch(ids),
                timeout=30.0  # 20 seconds timeout
            )
            
            # parse PubMed articles abstract content
            final_results = []
            abstract_list = abstracts.split("\n\n\n")
            for index, abstract in enumerate(abstract_list):
                abstract = abstract.replace('\n', '')
                abstract = re.sub(r'^\d+\.\s*', '', abstract)
                uid = article_results['uids'][index]
                if uid in article_results:
                    article_result = article_results[uid]
                    article_result['summary'] = abstract
                    final_results.append(article_result)

        except asyncio.TimeoutError:
            logger.warn(f"[PubmedSearch] query {query} timed out after 20 seconds")
            final_results = []
        except Exception as exc:
            logger.warn(f"[PubmedSearch] query {query} failed, exception", exc)
            final_results = []
        finally:
            # Ensure we close the session
            await self.pubmed_client.close()
            
        end_time = time.time()
        
        res['result'] = final_results
        logger.info(f"PubMed search engine query time cost {end_time - start_time}s get {len(final_results)}")
        yield res

    async def efetch(self, ids: str, db: str = "pubmed"):
        return await self.pubmed_client.efetch(ids, db)


class SymbolInputSchema(BaseModel):
    thought_process: str = Field(description='The explanation **in the working language** of why triggering this query.')
    symbol: str = Field(
        description='Ticker symbol (e.g., "AAPL").'
    )


class StockTimeSpaneQueryInputSchema(BaseModel):
    thought_process: str = Field(description='The explanation **in the working language** of why triggering this query.')
    symbol: str = Field(
        description='Ticker symbol (e.g., "AAPL").'
    )
    date_from: str = Field(
        description='Start date in YYYY-MM-DD format (e.g., "2023-12-01"). '
                    'If omitted, defaults to six months ago.'
    )
    date_to: str = Field(
        description='End date in YYYY-MM-DD format (e.g., "2024-01-01"). '
                    'If omitted, defaults to today.'
    )


class StockHistoricalPriceQuery(BaseTool):
    name: str = 'StockHistoricalPriceQuery'
    description: str = 'Fetch stock history price by symbol and date spane'
    input_schema: BaseModel = StockTimeSpaneQueryInputSchema
    strict: bool = True   

    fmp_client: FinancialModelinGprep = FinancialModelinGprep()

    def _format_symbol(self, symbol: str) -> str:
        # For HK symbol should like 06855.HK -> 6855.HK, not starts with 0
        if symbol.endswith('.HK'):
            # Remove leading zeros from the numeric part
            num_part = symbol.split('.')[0].lstrip('0')
            if not num_part:  # If all zeros, keep one zero
                num_part = '0'
            return f"{num_part}.HK"
        return symbol

    async def run(self, **kwarg):
        symbol = kwarg.get('symbol', '')
        date_from = kwarg.get('date_from', (datetime.now() - timedelta(days=180)).strftime('%Y-%m-%d'))
        date_to = kwarg.get('date_to',  datetime.now().strftime('%Y-%m-%d'))

        if symbol == '':
            yield {}
            return
        
        if date_from != '' and date_to != '':
            result = self.fmp_client.daily_char_eod(symbol=symbol, date_from=date_from, date_to=date_to)
        else:
            result = self.fmp_client.daily_char_eod(symbol=symbol)

        yield {
            "function": self.name,
            "params": kwarg,
            "result": result
        }


class StockNewsSearch(BaseTool):
    name: str = 'StockNewsSearch'
    description: str = 'Query stock news by symbol and date spane'
    input_schema: BaseModel = StockTimeSpaneQueryInputSchema
    strict: bool = True   

    fmp_client: FinancialModelinGprep = FinancialModelinGprep()

    async def run(self, **kwarg):
        symbol = kwarg.get('symbol', '')
        date_from = kwarg.get('date_from', (datetime.now() - timedelta(days=180)).strftime('%Y-%m-%d'))
        date_to = kwarg.get('date_to', datetime.now().strftime('%Y-%m-%d'))

        if symbol == '':
            yield {}
            return
        
        if date_from != '' and date_to != '':
            result = self.fmp_client.stock_news(tickers=[symbol], date_from=date_from, date_to=date_to)
        else:
            result = self.fmp_client.stock_news(tickers=[symbol])

        yield {
            "function": self.name,
            "params": kwarg,
            "result": result
        }

    
class WebpageReaderInputSchema(BaseModel):
    thought_process: str = Field(description='The explanation **in the working language** of why and what want to get from these web pages.')
    urls: List[int] = Field(
        description='Webpage url id.'
    )


class WebpageReader(BaseTool):
    name: str = 'WebpageReader'
    description: str = 'Webpage reader which can load the webpage content.'
    input_schema: BaseModel = WebpageReaderInputSchema

    async def run(self, **kwarg):
        urls = kwarg.get('urls', [])

        yield {
            "function": self.name,
            "params": kwarg,
            "result": urls
        }


class CompanyPressReleasesNewsQuery(BaseTool):
    name: str = 'CompanyPressReleasesNewsQuery'
    description: str = 'Query company press releases news by symbol and date spane'
    input_schema: BaseModel = StockTimeSpaneQueryInputSchema
    strict: bool = True   

    fmp_client: FinancialModelinGprep = FinancialModelinGprep()

    async def run(self, **kwarg):
        symbol = kwarg.get('symbol', '')

        if symbol == '':
            yield {}
            return
        
        result = self.fmp_client.press_releases(symbol=symbol)

        yield {
            "function": self.name,
            "params": kwarg,
            "result": result
        }


class CompanyInfoQuery(BaseTool):
    name: str = 'CompanyInfoQuery'
    description: str = 'Company detail information, i.e. market place, industry, exchange, current price.'
    input_schema: BaseModel = SymbolInputSchema

    fmp_client: FinancialModelinGprep = FinancialModelinGprep()

    async def run(self, **kwarg):
        symbol = kwarg.get('symbol', '')

        yield {
            "function": self.name,
            "params": kwarg,
            "result": self.fmp_client.company_profile(symbol=symbol)
        }

class FinancialStatementsPeriod(str, Enum):
    r"""Financial statements period."""
    ANNUAL = 'annual'
    QUARTER = 'quarter'
    

class FinancialStatementsInputSchema(BaseModel):
    symbol: str = Field(
        description='Ticker symbol (e.g., "AAPL").'
    )
    period: FinancialStatementsPeriod = Field(
        default=FinancialStatementsPeriod.ANNUAL,
        description='Financial statements period, only support annual and quarter. Default is annual'
    )
    limit: int = Field(
        default=1,
        description='Amount of financial statements, default is 1.'
    )
    thought_process: str = Field(description='The step by step explanation **in the working language** of why triggering this query.')


class FinancialStatements(BaseTool):
    name: str = 'FinancialStatements'
    description: str = "Query company's financial statements by stock symbol, support annual and quarter"
    input_schema: BaseModel = FinancialStatementsInputSchema
    strict: bool = True

    fmp_client: FinancialModelinGprep = FinancialModelinGprep()

    async def run(self, **kwarg):
        symbol = kwarg.get('symbol', '')

        if symbol == '':
            yield {}
            return

        period = kwarg.get('period', FinancialStatementsPeriod.ANNUAL.value)
        limit = kwarg.get('limit', 1)

        fincacial_statements = self.fmp_client.financial_statements_as_reported(symbol=symbol, period=period, limit=limit)
        
        # add special case
        stock_price = self.fmp_client.daily_char_eod(symbol=symbol)
        if stock_price:
            historical = stock_price.get('historical', [])
            if len(historical) > 0:
                close_price = historical[-1].get('close', None)
                if close_price is not None and fincacial_statements is not None:
                    commonstocksharesoutstanding = fincacial_statements[0].get('commonstocksharesoutstanding', 0)
                    fincacial_statements[0]['marketcapitalization'] = commonstocksharesoutstanding * close_price

        yield {
            "function": self.name,
            "params": kwarg,
            "result": fincacial_statements
        }


class ChinaCompanyFinancialStatements(BaseModel):
    symbol: str = Field(
        description='Ticker symbol (e.g., "0000001.SZ", "600276.SS").'
    )
    date_from: str = Field(
        description='Start date in YYYY-MM-DD format (e.g., "2023-12-01"). '
                    'If omitted, defaults to six months ago.'
    )
    date_to: str = Field(
        description='End date in YYYY-MM-DD format (e.g., "2024-01-01"). '
                    'If omitted, defaults to today.'
    )
    thought_process: str = Field(description='The step by step explanation **in the working language** of why triggering this query.')


class ChinaCompanyFinancialStatements(BaseTool):
    name: str = 'ChinaCompanyFinancialStatements'
    description: str = "Query China National Stock Exchange company's financial statements, i.e. 0000001.SZ"
    input_schema: BaseModel = ChinaCompanyFinancialStatements

    mairui: MaiRui = MaiRui()

    def _format_symbol(
        self,
        symbol: str
    ):
        if symbol.lower().endswith('ss'):
            symbol = symbol.split('.')[0] + ".SH"
        return symbol

    async def run(self, **kwarg):
        symbol = kwarg.get('symbol', '')
        symbol = self._format_symbol(symbol)

        date_from = kwarg.get('date_from', '')
        date_to = kwarg.get('date_to', '')
        
        # Convert date format from YYYY-MM-DD to YYYYMMDD
        if date_from:
            date_from = date_from.replace('-', '')
        if date_to:
            date_to = date_to.replace('-', '')

        yield {
            "function": self.name,
            "params": kwarg,
            "result": self.mairui.financial_statements(symbol=symbol, date_from=date_from, date_to=date_to)
        }


class StockGeneralSearchInputSchema(BaseModel):
    query: str = Field(
        description='Company name in English or stock symbol, , i.e. APPLE or Apple Inc. would fetch AAPL. 6855.HK would fetch Ascentage Pharma Group International.'
    )

class StockGeneralSearch(BaseTool):
    name: str = 'GeneralSearch'
    description: str = 'General search for symbol or company name in English, result only contains, symbol, name, currency, stock exchange'
    input_schema: BaseModel = StockGeneralSearchInputSchema
    strict: bool = True   

    fmp_client: FinancialModelinGprep = FinancialModelinGprep()

    async def run(self, **kwarg):
        query = kwarg.get('query', '')

        if query == '':
            yield {}
            return
        
        yield {
            "function": self.name,
            "params": kwarg,
            "result": self.fmp_client.general_search(query=query)
        }

class FinishedInputSchema(BaseModel):
    thought_process: str = Field(
        description="Step by step think and search finished reason in working language."
    )
    urls: List[int] = Field(
        description="Recommend web pages url id. Empty only when user's question don't need web searching (i.e. summarizing history messages, simple question) or already did the WebpageReader action."
    )
    

class Finished(BaseTool):
    name: str = 'Finished'
    description: str = 'Finsihment notice function.'
    input_schema: BaseModel = FinishedInputSchema

    async def run(self, **kwarg):
        urls = kwarg.get('urls', [])

        yield {
            "function": self.name,
            "params": kwarg,
            "result": urls
        }

class DatastoreFinishedInputSchema(BaseModel):
    thought_process: str = Field(
        description="Step by step think and search finished reason in working language."
    )
    tmp_ids: List[str] = Field(
        description="Selected datastore record **tmp_id**."
    )
    

class DatastoreFinished(BaseTool):
    name: str = 'DatastoreFinished'
    description: str = 'Finsihment notice function contains selected datastore records.'
    input_schema: BaseModel = DatastoreFinishedInputSchema

    async def run(self, **kwarg):
        tmp_ids = kwarg.get('tmp_ids', [])

        yield {
            "function": self.name,
            "params": kwarg,
            "result": tmp_ids
        }
