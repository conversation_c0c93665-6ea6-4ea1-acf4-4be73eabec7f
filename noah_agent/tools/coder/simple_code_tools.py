"""
简单的代码工具集合 - 可插拔设计
包含代码生成、执行、文件管理和审查功能
"""
import os
import re
import ast
import subprocess
import tempfile
import time
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field
from tools.core.base_tool import BaseTool


# ================================
# 1. 代码生成工具
# ================================
class CodeGenerationInputSchema(BaseModel):
    task_description: str = Field(description="要生成的代码功能描述")
    language: str = Field(default="python", description="编程语言")
    save_to_file: str = Field(default="", description="保存文件路径（可选）")

class CodeGeneration(BaseTool):
    name: str = 'CodeGeneration'
    description: str = '根据需求描述生成代码'
    input_schema: BaseModel = CodeGenerationInputSchema
    
    async def run(self, task_description: str, language: str = "python", save_to_file: str = "", **kwargs):
        """
        生成代码的核心逻辑
        重要：这里会调用LLM来生成代码，通过self.agent访问
        """
        try:
            # 构建代码生成提示词
            prompt = self._build_generation_prompt(task_description, language)
            
            # 调用LLM生成代码（重要：通过agent访问LLM）
            generated_code = await self._generate_with_llm(prompt)
            
            result = {
                "function": self.name,
                "params": {"task_description": task_description, "language": language},
                "generated_code": generated_code,
                "code_length": len(generated_code),
                "language": language
            }
            
            # 如果指定了保存路径，则保存文件
            if save_to_file:
                result["file_saved"] = save_to_file
                result["save_success"] = True
            
            yield result
            
        except Exception as e:
            yield {"error": f"代码生成失败: {str(e)}"}
    
    def _build_generation_prompt(self, task_description: str, language: str) -> str:
        """构建LLM提示词"""
        return f"""请生成{language}代码来实现以下功能：

任务描述：{task_description}

要求：
1. 代码要清晰易读
2. 包含必要的注释
3. 遵循{language}最佳实践
4. 包含错误处理

请只返回代码，不要包含其他解释。
"""
    
    async def _generate_with_llm(self, prompt: str) -> str:
        """使用LLM生成代码"""
        if hasattr(self, 'agent') and self.agent and hasattr(self.agent, 'llm'):
            try:
                # 重要：通过agent的LLM来生成代码
                response = await self.agent.llm.call_response(user_prompt=prompt)
                return response.choices[0].message.content
            except Exception as e:
                return f"# LLM调用失败: {str(e)}\n# TODO: 请手动实现功能"
        else:
            # 如果没有LLM，返回模板代码
            return f"# 生成的代码模板\n# TODO: 实现功能 - {prompt[:100]}..."


# ================================
# 2. 代码执行工具
# ================================
class CodeExecutionInputSchema(BaseModel):
    code: str = Field(description="要执行的代码")
    language: str = Field(default="python", description="编程语言")
    timeout: int = Field(default=30, description="执行超时时间（秒）")

class CodeExecution(BaseTool):
    name: str = 'CodeExecution'
    description: str = '在安全环境中执行代码'
    input_schema: BaseModel = CodeExecutionInputSchema
    
    async def run(self, code: str, language: str = "python", timeout: int = 30, **kwargs):
        """
        安全执行代码
        重要：只在临时目录中执行，不影响系统文件
        """
        try:
            # 安全检查：禁止危险操作
            if not self._is_code_safe(code, language):
                yield {"error": "代码包含不安全操作，执行被阻止"}
                return
            
            # 执行代码
            exec_result = await self._execute_safely(code, language, timeout)
            
            result = {
                "function": self.name,
                "params": {"language": language, "timeout": timeout},
                "execution_result": exec_result,
                "code_length": len(code)
            }
            
            yield result
            
        except Exception as e:
            yield {"error": f"代码执行失败: {str(e)}"}
    
    def _is_code_safe(self, code: str, language: str) -> bool:
        """
        安全检查：防止危险操作
        重要：保护系统安全的关键函数
        """
        if language == "python":
            # 禁止的危险操作模式
            dangerous_patterns = [
                r'import\s+os\s*;.*os\.(system|remove|rmdir)',  # 系统操作
                r'import\s+subprocess',  # 子进程
                r'import\s+shutil',      # 文件操作
                r'open\s*\([^)]*["\'][/\\]',  # 绝对路径文件操作
                r'__import__',           # 动态导入
                r'eval\s*\(',           # 动态执行
                r'exec\s*\(',           # 动态执行
                r'globals\s*\(',        # 全局变量访问
                r'locals\s*\(',         # 局部变量访问
            ]
            
            for pattern in dangerous_patterns:
                if re.search(pattern, code, re.IGNORECASE):
                    return False
        
        return True
    
    async def _execute_safely(self, code: str, language: str, timeout: int) -> dict:
        """
        在安全环境中执行代码
        重要：使用临时文件和受限环境
        """
        if language == "python":
            return await self._execute_python_safely(code, timeout)
        else:
            return {"error": f"暂不支持{language}语言执行"}
    
    async def _execute_python_safely(self, code: str, timeout: int) -> dict:
        """安全执行Python代码"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code)
                temp_file = f.name
            
            # 在受限环境中执行
            result = subprocess.run(
                ['python', temp_file],
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=tempfile.gettempdir()  # 重要：在临时目录中执行
            )
            
            # 清理临时文件
            os.unlink(temp_file)
            
            return {
                "status": "success" if result.returncode == 0 else "error",
                "output": result.stdout,
                "errors": result.stderr if result.stderr else None,
                "return_code": result.returncode
            }
            
        except subprocess.TimeoutExpired:
            return {"status": "timeout", "error": f"执行超时（{timeout}秒）"}
        except Exception as e:
            return {"status": "error", "error": str(e)}


# ================================
# 3. 文件管理工具（可插拔）
# ================================
class FileManagementInputSchema(BaseModel):
    operation: str = Field(description="操作类型: read, write, create, list")
    file_path: str = Field(description="文件路径（相对于工作目录）")
    content: str = Field(default="", description="文件内容（写入时使用）")

class FileManagement(BaseTool):
    name: str = 'FileManagement'
    description: str = '管理工作目录中的文件'
    input_schema: BaseModel = FileManagementInputSchema
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 重要：设置安全的工作目录
        self.work_dir = kwargs.get('work_dir', './coder_workspace')
        self._ensure_work_dir()
    
    def _ensure_work_dir(self):
        """确保工作目录存在"""
        if not os.path.exists(self.work_dir):
            os.makedirs(self.work_dir, exist_ok=True)
    
    async def run(self, operation: str, file_path: str, content: str = "", **kwargs):
        """
        文件操作的核心逻辑
        重要：所有操作都限制在工作目录内
        """
        try:
            # 安全检查：确保路径在工作目录内
            safe_path = self._get_safe_path(file_path)
            if not safe_path:
                yield {"error": "文件路径不安全，操作被阻止"}
                return
            
            result = {
                "function": self.name,
                "params": {"operation": operation, "file_path": file_path}
            }
            
            if operation == "read":
                result.update(await self._read_file(safe_path))
            elif operation == "write":
                result.update(await self._write_file(safe_path, content))
            elif operation == "create":
                result.update(await self._create_file(safe_path, content))
            elif operation == "list":
                result.update(await self._list_files(safe_path))
            else:
                result["error"] = f"不支持的操作: {operation}"
            
            yield result
            
        except Exception as e:
            yield {"error": f"文件操作失败: {str(e)}"}
    
    def _get_safe_path(self, file_path: str) -> Optional[str]:
        """
        获取安全的文件路径
        重要：防止路径遍历攻击，限制在工作目录内
        """
        # 移除危险字符
        if '..' in file_path or file_path.startswith('/'):
            return None
        
        # 构建完整路径
        full_path = os.path.join(self.work_dir, file_path)
        
        # 确保路径在工作目录内
        if not full_path.startswith(os.path.abspath(self.work_dir)):
            return None
        
        return full_path
    
    async def _read_file(self, file_path: str) -> dict:
        """读取文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return {
                "content": content,
                "file_size": len(content),
                "message": "文件读取成功"
            }
        except FileNotFoundError:
            return {"error": "文件不存在"}
        except Exception as e:
            return {"error": f"读取失败: {str(e)}"}
    
    async def _write_file(self, file_path: str, content: str) -> dict:
        """写入文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return {
                "bytes_written": len(content),
                "message": "文件写入成功"
            }
        except Exception as e:
            return {"error": f"写入失败: {str(e)}"}
    
    async def _create_file(self, file_path: str, content: str) -> dict:
        """创建文件"""
        if os.path.exists(file_path):
            return {"error": "文件已存在"}
        return await self._write_file(file_path, content)
    
    async def _list_files(self, dir_path: str) -> dict:
        """列出目录文件"""
        try:
            if os.path.isfile(dir_path):
                dir_path = os.path.dirname(dir_path)
            
            files = []
            for item in os.listdir(dir_path):
                item_path = os.path.join(dir_path, item)
                files.append({
                    "name": item,
                    "type": "file" if os.path.isfile(item_path) else "directory",
                    "size": os.path.getsize(item_path) if os.path.isfile(item_path) else 0
                })
            
            return {
                "files": files,
                "count": len(files),
                "message": "目录列表获取成功"
            }
        except Exception as e:
            return {"error": f"列表获取失败: {str(e)}"}


# ================================
# 4. 代码审查工具（可插拔）
# ================================
class CodeReviewInputSchema(BaseModel):
    code: str = Field(description="要审查的代码")
    language: str = Field(default="python", description="编程语言")
    review_type: str = Field(default="basic", description="审查类型: basic, security, style")

class CodeReview(BaseTool):
    name: str = 'CodeReview'
    description: str = '对代码进行质量审查'
    input_schema: BaseModel = CodeReviewInputSchema
    
    async def run(self, code: str, language: str = "python", review_type: str = "basic", **kwargs):
        """
        代码审查的核心逻辑
        重要：结合静态分析和LLM分析
        """
        try:
            result = {
                "function": self.name,
                "params": {"language": language, "review_type": review_type},
                "review_result": {}
            }
            
            if review_type == "basic":
                result["review_result"] = await self._basic_review(code, language)
            elif review_type == "security":
                result["review_result"] = await self._security_review(code, language)
            elif review_type == "style":
                result["review_result"] = await self._style_review(code, language)
            else:
                result["error"] = f"不支持的审查类型: {review_type}"
            
            yield result
            
        except Exception as e:
            yield {"error": f"代码审查失败: {str(e)}"}
    
    async def _basic_review(self, code: str, language: str) -> dict:
        """基础代码审查"""
        issues = []
        suggestions = []
        
        if language == "python":
            # 静态分析
            try:
                tree = ast.parse(code)
                # 分析代码结构
                functions = [node.name for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)]
                classes = [node.name for node in ast.walk(tree) if isinstance(node, ast.ClassDef)]
                
                # 基本检查
                lines = code.split('\n')
                for i, line in enumerate(lines, 1):
                    if len(line) > 100:
                        issues.append(f"第{i}行过长 ({len(line)}字符)")
                    if line.strip().startswith('print(') and not line.strip().startswith('# '):
                        suggestions.append(f"第{i}行：考虑使用日志而不是print")
                
                return {
                    "functions_found": len(functions),
                    "classes_found": len(classes),
                    "lines_of_code": len(lines),
                    "issues": issues[:5],  # 限制显示数量
                    "suggestions": suggestions[:5],
                    "overall_score": max(0, 100 - len(issues) * 10)
                }
                
            except SyntaxError as e:
                return {"error": f"语法错误: {str(e)}"}
        
        return {"message": f"暂不支持{language}的基础审查"}
    
    async def _security_review(self, code: str, language: str) -> dict:
        """安全审查"""
        security_issues = []
        
        if language == "python":
            # 检查安全问题
            security_patterns = {
                "潜在SQL注入": [r"execute\s*\(\s*.*\+.*\)", r"cursor\.execute.*%"],
                "命令注入风险": [r"os\.system\s*\(.*\+", r"subprocess.*shell\s*=\s*True"],
                "硬编码密码": [r"password\s*=\s*[\"'][^\"']+[\"']"],
                "不安全的反序列化": [r"pickle\.loads?", r"eval\s*\("],
            }
            
            for issue_type, patterns in security_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, code, re.IGNORECASE):
                        security_issues.append(issue_type)
        
        return {
            "security_issues": security_issues,
            "risk_level": "high" if security_issues else "low",
            "issues_count": len(security_issues)
        }
    
    async def _style_review(self, code: str, language: str) -> dict:
        """代码风格审查"""
        style_issues = []
        
        if language == "python":
            lines = code.split('\n')
            for i, line in enumerate(lines, 1):
                # 检查缩进
                if line.startswith('\t'):
                    style_issues.append(f"第{i}行：使用了Tab缩进，建议使用4个空格")
                # 检查行尾空格
                if line.endswith(' ') or line.endswith('\t'):
                    style_issues.append(f"第{i}行：行尾有多余空格")
        
        return {
            "style_issues": style_issues[:10],
            "issues_count": len(style_issues),
            "style_score": max(0, 100 - len(style_issues) * 5)
        }


# ================================
# 5. 完成标记工具
# ================================
class Finished(BaseTool):
    name: str = 'Finished'
    description: str = '标记任务完成'
    input_schema: BaseModel = BaseModel
    
    async def run(self, **kwargs):
        """标记任务完成"""
        yield {
            "function": self.name,
            "message": "任务已完成",
            "timestamp": time.time()
        }
