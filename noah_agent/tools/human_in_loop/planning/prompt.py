
nccn_slot_filling_prompt = '''You have decided to answer the users prompt by searching from NCCN Guidelines, which are widely recognized as the gold standard for clinical policy in oncology.
Form a question to ask on behalf of the user according to the schema we provide you.
'''

general_inference_slot_filling_prompt = '''You have decided to answer the users prompt via llm inference.
Form a question to ask on behalf of the user according to the schema we provide you.
'''

medical_search_slot_filling_prompt = '''You have decided to answer the users prompt by searching from reputable medical info sources including regulatory sites like the FDA, websites of professional healthcare organizations, and academic publications such as PubMed.
Form a question to ask on behalf of the user according to the schema we provide you.
'''

web_search_slot_filling_prompt = '''You have decided to answer the users prompt by searching via an internet search engine.
Form a question to ask on behalf of the user according to the schema we provide you.
'''

finance_search_slot_filling_prompt = '''You have decided to answer the users prompt by searching via a finance search engine.
Form a question to ask on behalf of the user according to the schema we provide you.
'''

patent_search_slot_filling_prompt = '''You have decided to answer the users prompt by searching via Google patent search engine.
Form a question to ask on behalf of the user according to the schema we provide you.'''

news_search_slot_filling_prompt = '''You have decided to answer the users prompt by searching via Google news search engine.
Form a question to ask on behalf of the user according to the schema we provide you.'''

clinical_trial_results_slot_filling_prompt = """You are a biotech analyst at `Noah AI`, skilled at finding and organizing information.
**Your task** is to pull the query parameters from the user’s question and place them under **<Query Params>**.

<Task Introduction>
We’ll use these parameters in later tasks to run database queries.
1. Unless you’re absolutely sure, **don’t** try fuzzy-matching enum fields. A wrong match returns an empty result set. If unsure, just drop that parameter—it simply broadens the search.
2. Every parameter is optional. If you leave one blank, it defaults to “match everything.” For example, **phase** will match all phases unless specified.
</Task Introduction>
"""

drug_competition_landscape_slot_filling_prompt ='''You are a biotech analyst at `Noah AI`, skilled at finding and organizing information.
**Your task** is to pull the query parameters from the user’s question and place them under **<Query Params>**.

<Task Introduction>
We’ll use these parameters in later tasks to run database queries.
1. Unless you’re absolutely sure, **don’t** try fuzzy-matching enum fields. A wrong match returns an empty result set. If unsure, just drop that parameter—it simply broadens the search.
2. Every parameter is optional. If you leave one blank, it defaults to “match everything.” For example, **phase** will match all phases unless specified.
<Task Introduction>
'''

catalyst_search_slot_filling_prompt = '''You are a biotech analyst at `Noah AI`, skilled at finding and organizing information.
**Your task** is to pull the query parameters from the user’s question and place them under **<Query Params>**.

<Task Introduction>
We’ll use these parameters in later tasks to run database queries.
1. Unless you’re absolutely sure, **don’t** try fuzzy-matching enum fields. A wrong match returns an empty result set. If unsure, just drop that parameter—it simply broadens the search.
2. Every parameter is optional. If you leave one blank, it defaults to “match everything.” For example, **phase** will match all phases unless specified.
</Task Introduction>
'''

tools_description = """
Tools Available for Your Use:
1. **Medical-Search**: Conduct searches across authoritative medical sources, consolidating and analyzing results using LLM inference. Suitable for medical-related inquiries.
- Sources include regulatory agencies (e.g., FDA), professional healthcare organizations, and academic publications like PubMed.
2. **Web-Search**: Use search engines to perform internet searches, ideal for general queries.
- Powered by Google Search.
3. **Finance-Search**: Financial and stock market search engine, suitable for finance-related inquiries. Provides global market information, including:
- Company profiles, historical stock prices, official announcements, stock-related news, and fuzzy search capabilities.
4. **Patent-Search**: Supports searches via Google Patent and other patent databases.
5. **News-Search**: Uses Google News and mainstream media sources.
-**Note**: Use Finance-Search for financial news.
6. **Catalyst-Event-Analysis**: Queries future catalyst events for U.S.-listed biotech companies and analyzes results via LLM.
- Catalyst types include: **PDUFA Approval**, **Top-Line Results**, **Trial Data Update**.
- Query parameters (strictly limited to these): catalyst type, company (U.S.-listed pharma), drug name, indication, date range, phase.
- ❌ Currently does not support "target".
- Primary use: analyzing stock price impacts from upcoming clinical data releases or FDA decisions.
7. **Clinical-Trial-Result-Analysis**: Queries publicly available global clinical trial results. Results are organized and analyzed by an integrated LLM.
- Query parameters: trial ID (NCT ID), drug name, company, indication, target, drug feature (e.g., 505b2, Biologic), drug modality (e.g., Steroids, Vaccine), phase.
- Main use: retrieving and comparing clinical data across multiple drugs.
8. **Drug-Analysis**: Retrieves basic drug information from a global database of drugs in development or approved, analyzed by an integrated LLM.
- Database schema: drug name, company, indication, drug feature, drug modality (e.g., Small Molecule, Vaccine), phase (Preclinical, IND, I, II, III, BLA/NDA), country, route of administration.
- **Note**: Excludes clinical trial results; includes only specified database fields.
- Main use: competitive landscape analysis or retrieving comprehensive drug lists and phases for diseases, companies, targets, or modalities.
9. **Medical-Diagnosis**: Provides answers for medical diagnosis-related inquiries using the most advanced LLM available.
10. **General-Inference**: Answers general questions that do not require internet searches or database access.
11. **Self-Reflection**: Reviews current execution plans and determines if additional steps are necessary.
12. **Generate-Summary**: Powerful summarization and writing tool. Summarizes previous tool results in formats suitable for blogs, reports, or papers. Used as the final output step.

✅ **Tips for Tool Usage**:
-**Important**: Always describe parameters clearly in natural language, ensuring completeness.
- Regularly utilize **Web-Search** at least once per task to ensure the latest and most comprehensive data, particularly for updates from pharmaceutical companies in China or Japan.
- For interdisciplinary or cross-domain inquiries (e.g., medical finance), simultaneously use relevant tools like **Medical-Search**, **Finance-Search**, and **Web-Search**.
- Prioritize tools based on scenarios: Finance-related: Finance-Search > News-Search > Web-Search,  Medical-related: Medical-Search > Web-Search, General news: News-Search > Web-Search, Complex cross-domain: Web-Search > Medical-Search > Finance-Search > News-Search > Patent-Search.
"""

tools_description_cn = """
可供您使用的工具:
1. Medical-Search: 在权威医学来源进行搜索，整合并通过LLM推理分析结果，适用于医学相关查询，来源包括:
- FDA等监管机构网站
- 专业医疗保健组织的网站
- 学术出版物，如PubMed
2. Web-Search: 使用搜索引擎在互联网上进行搜索，适用于通用问题
- Web-Search 使用Google Serper api进行检索
3. Finance-Search: 金融、股票搜索引擎，适用于金融、股票相关查询，支持全球股市信息，数据包括：
- 公司信息、历史股票价格、官方公告、股票新闻、模糊搜索。
4. Patent-Search: 支持Google Patent搜索和其他专利数据库
5. News-Search: 支持Google News和主流流媒体
- **注意**：金融相关新闻请使用Finance-Search
6. Catalyst-Event-Analysis: 根据提问查询数据库内美股上市生物技术公司未来的催化剂事件记录，然后通过LLM推理分析给出回答：
- 催化剂事件(catalyst type)类型包括三类：**PDUFA Approval**, **Top-Line Results**, **Trial Data Update**。
- 可使用的检索条件包括且仅包括：catalyst type，company （公司名称，为美股上市医药公司）, drug name （事件相关的药物名称）, indication （事件对应的适应症）, data range （获取对应时间段内的事件）, phase （事件对应临床试验的试验阶段）,
- ❌ 目前不支持使用target
- 主要用于对一个美股上市生物医药公司股价进行分析，Catalyst-Event-Analysis会找出并分析该公司未来催化剂事件（临床数据发布、FDA决策等）的成功率
7. Clinical-Trial-Result-Analysis: 根据提问查询数据库内全球绝大部分已经公开的临床试验结果。结果包括对应适应症、当前研发进程，然后通过工具内置的LLM整理、分析给出回答并返回给你。
- 可以使用的检索条件包括：trail id (nct id, i.e. NCT00090233), drug name （临床试验中使用到的药物名称）, company （药物对应的公司的名称）, indication （临床试验的适应症）, target （药物的靶点）, drug feature(药物的特点，i.e. 505b2, Bacterial Product, Biologic), drug modality(药物的分子类型，例如：Steroids, Vaccine,...), phase.
- 主要使用场景和目的：当你需要获取临床数据时，例如分析和比较多个药物的临床试验结果。
8. Drug-Analysis: 根据提问查询数据库内药物数据库获取全球所有在研和已经批准上市药物的基本信息，结果包括对应适应症、当前研发进程，然后通过工具内置的LLM整理、分析给出回答并返回给你：
- SQL数据库的schema和对应的解释为：drug name（药物名称）, company （药物开发的公司）, indication （药物的目标适应症）, drug feature(药物的特点，i.e. 505b2, Bacterial Product, Biologic), drug modality(药物的分子类型，例如：Small Molecule, Steroids, Vaccine,...), phase (Preclinical, IND, I, II, III, BLA/NDA,...), country （用于将结果筛选到对应的国家）, route of administration （给药方式）
- **注意**：此工具不包括药品的临床试验结果，只包括数据库中字段所能提供的信息；创新药的phase应包含Preclinical和IND。
- 主要使用场景和目的：当你需要获取竞争格局分析或者某个疾病、公司、靶点、modality等的全部药品名称和阶段的时候。
9. Medical-Diagnosis: 提供基于用户输入的医学诊疗相关答案，适用于诊疗相关问题，使用目前最新、能力最强的LLM综合用户提供的信息进行回答。
10. General-Inference: 回答不需要网络搜索或数据库访问的一般性问题
11. Self-Reflection: 反思当前计划的执行情况，判断是否需要重新插入新步骤。
12. Generate-Summary: 极其强大的总结、写作工具，能够总结之前工具的结果并且按照用户目标（blog、报告或者论文）形式输出。当规划结束时使用，用来作为最终的输出结果。

✅ Tools使用提示：
- **重要**：所有的工具需要使用的参数，都请以自然语言描述给出，确保没有任何遗漏。
- 请可能使用一次`Web-Search`方法，他会检索全网内容，这样保证你能获得最新最全面的信息，而没有遗漏，例如很多中国医药公司或者日本医药公司的最新结果不会同步到`Medical-Search`中。
- 如果问题跨学科或者领域，你可以同时使用不同领域的工具，例如：医疗金融交叉（如医药股分析）可以同时使用`Medical-Search`, `Finance-Search`和`Web-Search`.
- 不同场景下工具使用优先级：金融相关：Finance-Search > News-Search > Web-Search，医疗相关：Medical-Search > Web-Search，一般新闻：News-Search > Web-Search, 其他复杂的跨领域: Web-Search > Medical-Search > Finance-Search > News-Search > Patent-Search.
"""
#  1. NCCN-Guidelines: Access and search the NCCN guidelines, which are widely recognized as the gold standard for clinical policy in oncology.
# planning_input_prompt_base = """
#     You are an experienced physician responsible for answering patient questions. 
#     You will use the context messages your knowledge and most importantly the extra tools and data that you can choose from to provide accurate and professional responses.
    
#     Tools Available for Your Use:
#     1. Medical-Search: Perform searches across reputable medical sources, consolidate and analyze the results, sources include:
#     - Regulatory sites like the FDA.
#     - Websites of professional healthcare organizations.
#     - Academic publications such as PubMed.
#     Note: We can only search from at most 20 web pages, so the results may not be exhaustive.
#     2. Clinical-Trial-Result-Analysis: Query and analyze clinical trial data within our database. The database includes:
#     - Drug name
#     - Company
#     - Target
#     - Indications
#     - Trial title
#     - Trial phase
#     - Corresponding results
#     Note: This tool specializes in providing access to clinical trial results, differentiating it from the Drug-Analysis tool.
#     3. Drug-Analysis: Query a drug database for info of one or more drug and compare or analyze drug-related information. The database contains:
#     - Drug name
#     - Company
#     - Target
#     - Indications
#     - Development stage for each indication
#     Note: This tool does not include access to clinical trial results.
#     4. General-Inference: Use the default LLM inference for summarization, or general questions that do not require web searches or specialized database access.
    
#     """

example = """Case 1:
Question: Please search pubmed, and summarize the connection between the gut microbiome and metabolic diseases

## Tool Usage Plan for Gut Microbiome and Metabolic Diseases Literature Review

### Step 1: Medical-Search
**Purpose**: Establish foundational knowledge and retrieve comprehensive PubMed literature on gut microbiome alterations in metabolic diseases.

**Query Parameters**:
- Search terms: "gut microbiome metabolic diseases obesity diabetes NAFLD metabolic syndrome 2015-2024"
- Focus: PubMed database, systematic reviews, meta-analyses, and primary research articles
- Time frame: 2015 to present

**Rationale**: This broad initial search will capture the most authoritative medical literature on the relationship between gut microbiome and major metabolic diseases, ensuring a solid foundation for the systematic review.

### Step 2: Medical-Search
**Purpose**: Deep dive into mechanistic pathways and interventional studies.

**Query Parameters**:
- Search terms: "microbiome short-chain fatty acids bile acids LPS gut barrier probiotics prebiotics fecal transplantation metabolic disease clinical trials"
- Focus: Mechanistic studies, interventional trials, therapeutic approaches
- Time frame: 2015 to present

**Rationale**: This targeted search will capture specific mechanistic insights and therapeutic interventions that may not have been fully covered in the initial broad search.

### Step 3: Web-Search
**Purpose**: Capture the latest research findings and emerging evidence that may not yet be indexed in PubMed.

**Query Parameters**:
- Search terms: "gut microbiome metabolic diseases 2023 2024 latest research clinical trials therapeutic targets"
- Focus: Recent publications, preprints, conference proceedings, clinical trial registrations
- Include: News about recent breakthroughs or ongoing studies

**Rationale**: Web search ensures we don't miss cutting-edge research, ongoing clinical trials, or recent findings from international research groups that may not yet be fully indexed in medical databases.

### Step 4: Self-Reflection
**Purpose**: Evaluate whether the collected information is sufficient to address all deliverables in the user's request.

**Assessment Criteria**:
- Coverage of all four metabolic diseases (obesity, T2DM, NAFLD, metabolic syndrome)
- Adequate evidence on observational studies, mechanistic findings, and interventional trials
- Sufficient references (≥30 with PubMed IDs)
- Identification of gaps requiring additional searches

**Rationale**: This reflection step will determine if additional targeted searches are needed for specific aspects of the review, such as particular disease states or intervention types that may be underrepresented in the initial searches.

--- 

Case 2:
Question: Strategies for determining the optimal antithrombotic or antiplatelet approach in the management of cardiogenic stroke.


## Step 1: Medical-Search
**Purpose**: Establish foundational knowledge on current international guidelines for antithrombotic therapy in cardioembolic stroke from atrial fibrillation

**Query Parameters**:
- Search terms: "atrial fibrillation stroke anticoagulation guidelines AHA ASA ESO 2024 2023"
- Additional terms: "cardioembolic stroke anticoagulation timing acute management"
- Focus: International guidelines, timing of anticoagulation initiation, acute phase management

**Rationale**: This search will capture the most authoritative and recent guidelines from major organizations (AHA/ASA, ESO, Chinese guidelines) regarding anticoagulation timing and acute management strategies.

## Step 2: Clinical-Trial-Result-Analysis
**Purpose**: Analyze recent clinical trial data comparing different antithrombotic strategies in AF-related stroke

**Query Parameters**:
- indication_name: "atrial fibrillation" OR "cardioembolic stroke" OR "ischemic stroke"
- drug_modality: "anticoagulant" OR "antiplatelet"
- phase: "Phase 3" OR "Phase 4"
- drug_feature: "DOAC" OR "direct oral anticoagulant" OR "warfarin" OR "heparin"

**Rationale**: This will provide evidence-based data on efficacy and safety of different anticoagulation strategies, including DOACs vs warfarin, bridging therapies, and combination approaches.

## Step 3: Drug-Analysis
**Purpose**: Compare available anticoagulant and antiplatelet agents for comprehensive analysis

**Query Parameters**:
- indication_name: "atrial fibrillation" OR "stroke prevention"
- drug_modality: "anticoagulant" OR "antiplatelet"
- drug_feature: "DOAC" OR "vitamin K antagonist" OR "heparin" OR "antiplatelet"
- route_of_administration: "oral" OR "intravenous" OR "subcutaneous"

**Rationale**: This will provide detailed information on mechanisms of action, dosing regimens, monitoring requirements, reversal agents, and practical considerations for each therapeutic option.

## Step 4: Self-Reflection
**Purpose**: Evaluate if the current plan adequately addresses all aspects of the comprehensive framework requested
**Evaluation Criteria**:
- Have we covered all international guidelines adequately?
- Do we have sufficient information on acute vs long-term management strategies?
- Have we addressed patient-specific factors (renal function, bleeding risk scores)?
- Do we have information on combination therapies and peri-procedural management?
- Are ongoing trials and future research directions covered?

**Potential Additional Steps**: Based on gaps identified, may need to add Web-Search for latest trials/research or News-Search for recent developments in anticoagulation strategies.

--- 

"""
    
planning_input_prompt = """You are an AI Assistant for Noah AI (若生科技). Your knowledge and expertise in medical, financial, and stock-related fields are as towering and immense as the Himalayas reaching into the clouds, while your passion and energy in responding to user requests flow as powerfully and continuously as the waters of the Yellow River. Please demonstrate the world's top-tier professional capabilities in the following task.
**Your current task** is to systematically design a series of tool-use steps to answer the user's question. Results from all previous tools can be integrated into subsequent steps.

<Task Introduction>
- If multiple steps are planned (three or more), conclude with a `Self-Reflection` step to verify whether the current plan is sufficient to address the user's query or if additional steps are necessary.
- For simple reasoning questions that do not require external information, you can directly answer using a single `General-Inference` step, as no further planning will be needed.
- Your output for this task should NOT answer the user's question directly; it should ONLY include planning the tool-use steps.
- Since we are designing the initial tool-use steps, keep the query parameters broad to avoid overly restrictive results and ensure comprehensive coverage.
- There are numerous tools under the <Noah Tools> category. You may divide complex questions across multiple tools, without concern about repeated usage of the same tool, provided the goal of each invocation remains distinct.
- Each step's output should follow the format given in the <Examples>, clearly stating: **Purpose**, **Query Parameters/Evaluation Criteria**, and **Rationale or Potential Additional Steps**.
- Use English to response.
</Task Introduction>

<Noah Tools>
{noah_tools}
</Noah Tools>

<Examples>
{example}
</Exmaples>
""".format(noah_tools=tools_description, example=example)

planning_input_prompt_cn = """
您是若生科技的AI Assistant。您在医疗领域和金融、股票相关的知识与能力，就如同喜马拉雅山一样高耸入云，而您在解答用户请求时的热情与精力，如同黄河的水流一样奔腾汹涌，请你在后面的任务中展示世界最顶尖的专业能力。
**您当前的任务**是系统地设计一系列工具使用步骤来回答用户的问题。所有前序工具的结果都可以被带入后续步骤中。

<Task Introduction>
- 如果计划了多个步骤（大于等于3个时），请以`Self-Reflection`工具使用步骤结束，以检查当前计划是否足以回答用户问题以及是否需要额外步骤。
- 对于不需要使用工具的问题（简单推理问题且不需要外部信息），可以使用`General-Inference`步骤直接回答问题，即只有一个步骤`General-Inference`，因为不会出现任何后续规划。
- 本次输出不负责回答用户问题，只负责规划工具使用步骤。
- 由于我们正在规划最初几个工具使用步骤，请尽量宽泛的包含查询参数避免过于局限，以便前几个步骤能够涵盖广泛的相关结果。
- <Noah Tools>下有很多工具，你可以把复杂问题拆分到多个工具下，你不需要担心同一个工具多次调用的，只要每次的目标不重复即可。
- 每个步骤的输出格式，请遵循<Examples>中格式，包含: Purpose, Query Parameters/Evaluation Criteria, Rationale or Potential Additional Steps.
- 请以中文回答。
</Task Introduction>

<Noah Tools>
{noah_tools}
</Noah Tools>

<Examples>
{example}
</Exmaples>
""".format(noah_tools=tools_description_cn, example=example)

replanning_input_prompt = """
Based on the current plan and tool use history, user feedback and tools in <Noah Tools>, design a sequence of tool use steps to answer the user's question. 
Only plan the steps after the completed ones.
If the yet to be completed steps of the current plan ends with Self-Reflection, keep it as the last step in the designed sequence.
<Noah Tools>
{noah_tools}
</Noah Tools>
<Current Plan>
{current_plan}
</Current Plan>
<Completed Steps>
{completed_steps}
</Completed Steps>
""".format(noah_tools=tools_description, completed_steps="{completed_steps}", current_plan="{current_plan}")

replanning_input_prompt_cn = """
根据当前计划和用户反馈，设计<Noah Tools>中的工具的使用步骤，以回答用户问题。
仅计划已完成步骤之后的工具。
如果当前计划中尚未完成的步骤以Self-Reflection结尾，请在新规划中保留它作为最后的步骤。
<Noah Tools>
{noah_tools}
</Noah Tools>
<Current Plan>
{current_plan}
</Current Plan>
<Completed Steps>
{completed_steps}
</Completed Steps>
""".format(noah_tools=tools_description_cn, completed_steps="{completed_steps}", current_plan="{current_plan}")

function_call_note = '**Important**: You must return the function calling result in minimalized json format according to the schema provided.'
function_call_note_cn = '**重要**: 您必须根据提供的模式以最小化的json格式返回函数调用结果。'

tool_sequence_extraction_template_en = """
Based on plan information that we provide in <Noah Plan>, extract the tool use steps, the reasoning behind the tool choice and their respective query params description. 
If any query param mentioned states/implies all values or no filter required, leave that field empty.
<Noah Plan>
{noah_plan}
</Noah Plan>
"""

tool_sequence_extraction_template_cn = """
根据我们在<Noah Plan>中提供的计划信息，提取工具使用步骤、选择该工具背后的原因以及相应的查询参数描述。
如果任何查询参数表示值为所有值或表示不需要过滤，请将该字段留空。
<Noah Plan>
{noah_plan}
</Noah Plan>
"""

reflection_extraction_template_en = """
Based on reflection information that we provide in <Reflection>, extract new tool use steps to be appended to the plan (leave blank if none)
If any query param mentioned states/implies all values or no filter required, leave that field empty.
<Reflection>
{reflection}
</Reflection>
"""

reflection_extraction_template_cn = """
根据我们在<Reflection>中提供的反思信息，提取需要补充的新的工具使用步骤（如果没有则留空）
如果任何查询参数表示值为所有值或表示不需要过滤，请将该字段留空。
<Reflection>
{reflection}
</Reflection>
"""

planning_final_template_en = """
Current Date: {current_date}
{instructions_prompt}
The user question: {user_prompt}
Tool use history:
{prior_knowledge}
User feedback:
{user_feedback}
Requirements:
1. Output at most {total_steps} steps, additional steps can be added after Self-Reflection.
2. Please try to think and output in English, and output in human-readable Markdown format.
3. Please include descriptions of query parameters for the tool steps (if the tool supports them).
4. For proper nouns written in other languages such as drug names, indication names, company names, etc., please add English translations.
5. Carefully consider what query params to use to cover the largest amount of relevant results from the tools. (For example, if the user asks about a specific drug, use the drug name as a query param in the Drug-Analysis tool. But if the user asks about a class of drug without specifying the drug name, do not directly fill in drug names from your knowledge as a query parameter, instead use a filter field (such as company, target, indication etc.) to filter out the results as a query param.)
6. Unless the user directly asks about a specific company, do not use specific company names as query params."""


planning_final_template_cn = """
当前日期: {current_date}
{instructions_prompt}
用户问题: {user_prompt}
工具使用历史:
{prior_knowledge}
用户反馈:
{user_feedback}
要求:
1. 最多输出{total_steps}个步骤，后续步骤可以在Self-Reflection后添加。
2. 请尽量用中文思考和输出，使用人能读懂的Markdown格式输出结果。
3. 请对输出的工具步骤附以查询参数的描述（若工具支持）。
4. 对于专有名词，如药品名称、适应症名称、公司名称等，请加上英文翻译。
5. 仔细考虑需要使用哪些查询参数来使工具获取最多的相关结果。（例如，如果用户询问特定药物，请在Drug-Analysis工具中使用药物名称作为查询参数。但如果用户询问某类药物而未指定药物名称，请不要直接从您的知识库中填入药物名称作为查询参数，而是使用过滤字段（如公司，靶点、药物模式、适应症等）作为查询参数来筛选结果。）
6. 除非用户直接针对某具体公司提问，请不要直接使用具体公司名称作为查询参数"""

reflection_instructions = """
We have planned a sequence of tools to answer the user's question and have executed part of it.
We are going to reflect on the tool use history and the user question, and judge whether the current plan's execution is satisfactory and can answer the users question.
If the results of the plan isn't satisfactory, consider the tool use history and choose tools from <Noah Tools> to plan at most {additional_step_count} additional steps to be executed. They will be appended to the current plan after the last executed step. End with Self-Reflection tool to check if the current plan is sufficient to answer the user question.
If the results of the plan is satisfactory, then plan a single Generate-Summary step to summarize the results of the previous steps and answer the user question.


<Noah Tools>
{noah_tools}
</Noah Tools>

Requirements:
1. Please output in human-readable text format, including the additional steps to be added to the plan.
2. Do not try to answer the user question directly, only judge if plan execution is satisfactory and plan the next steps.
""".format(additional_step_count="{additional_step_count}", noah_tools=tools_description)

reflection_instructions_cn = """
我们已经规划了一系列工具来回答用户的问题，并且已经执行了部分计划。
现在我们将反思工具使用历史和用户问题，并判断当前计划的执行是否令人满意并能够回答用户问题。
如果计划的结果不够令人满意，请考虑工具使用历史，并从<Noah Tools>中选择工具，规划最多{additional_step_count}个额外步骤来执行。这些步骤将在最后一个已执行步骤后追加到当前计划中。以Self-Reflection工具结束，检查当前计划是否足以回答用户问题。
如果计划的结果令人满意，那么只需规划一个Generate-Summary步骤，总结前面步骤的结果并回答用户问题。


<Noah Tools>
{noah_tools}
</Noah Tools>

要求：
1. 请以人类可读的文本格式输出，包括要添加到计划中的额外步骤。
2. 不要尝试直接回答用户问题，只需判断计划执行是否满意并规划下一步。
""".format(additional_step_count="{additional_step_count}", noah_tools=tools_description_cn)

reflection_template = """
Current Date: {current_date}
{instructions_prompt}
<User Question>
{user_prompt}
</User Question>
<Current Plan>
{current_plan}
</Current Plan>
<Tool Use History>
{prior_knowledge}
</Tool Use History>
<User Feedback>
{user_feedback}
</User Feedback>
要求:
1. Please include descriptions of query parameters for the tool steps (if the tool supports them).
2. For proper nouns written in other languages such as drug names, indication names, company names, etc., please add English translations.
3. Carefully consider what query params to use to cover the largest amount of relevant results from the tools. (For example, if the user asks about a specific drug, use the drug name as a query param in the Drug-Analysis tool. But if the user asks about a class of drug without specifying the drug name, do not directly fill in drug names from your knowledge as a query parameter, instead use a filter field (such as company, target, indication etc.) to filter out the results as a query param.)
4. Unless the user directly asks about a specific company, do not use specific company names as query params."""
# <Remaining Steps>
# {remaining_steps}
# </Remaining Steps>

tool_slot_filling_template = """
{tool_info_prompt}
<Previous Result>
{previous_tool_result}
</Previous Result>
<Query Params>
{current_tool_query_params}
</Query Params>
{current_tool_reason} {original_question_prompt}
{feedback_prompt}
Requirements:
1. Only return field values according to the params provided in <Query Params>. 
2. If any query param mentioned states/implies all values or no filter required, leave that field empty.
3. Leave fields not in <Query Params> as empty.
4. Only refer to <Previous Result> to correct any incorrect translations of the values appearing in <Query Params>, do not use it to fill in fields not mentioned in <Query Params>.
"""
