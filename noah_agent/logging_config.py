import sys
import logging.config
import logging.handlers

from datetime import datetime
from contextvars import ContextVar

log_id_var: ContextVar[str] = ContextVar('log_id', default='')
task_id_var: ContextVar[str] = ContextVar('task_id', default='')

class LogIDFilter(logging.Filter):
    def filter(self, record):
        record.log_id = log_id_var.get('') or 'no-log-id'
        return True

current_date = datetime.now().strftime('%Y-%m-%d')

LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'loggers': {
        'root': {
            'level': 'INFO',
            'handlers': ['console', 'error_file'],
        },
        'uvicorn': {
            'level': 'INFO',
            'handlers': ['console', 'error_file'],
            'propagate': False,
        },
        'uvicorn.error': {
            'level': 'INFO',
            'handlers': ['console', 'error_file'],
            'propagate': False,
        },
        'uvicorn.access': {
            'level': 'INFO',
            'handlers': ['console', 'info_file'],
            'propagate': False,
        },
        # model 
        'sqlalchemy': {
            "level": "CRITICAL",
            "handlers": ['console', 'info_file'],
            "propagate": False,
        },
        'azure': {
            "level": "CRITICAL",
            "handlers": ['console', 'info_file'],
            "propagate": False,
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'generic',
            'stream': sys.stderr,
            'filters': ['log_id_filter']
        },
        'error_file': {
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'formatter': 'generic',
            'filename': f'logs/agent.log.{current_date}',
            'when': 'D',
            'interval': 1,
            'backupCount': 0,
            'encoding': None,
            'delay': False,
            'utc': False,
            'atTime': None,
            'filters': ['log_id_filter']
        },
        'info_file': {
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'formatter': 'access',
            'filename': f'logs/agent.log.{current_date}',
            'when': 'D',
            'interval': 1,
            'backupCount': 0,
            'encoding': None,
            'delay': False,
            'utc': False,
            'atTime': None,
            'filters': ['log_id_filter']
        },
    },
    'formatters': {
        'generic': {
            'format': '%(asctime)s [%(log_id)s] [%(module)s:%(lineno)d] [%(levelname)s] %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S %z',
            'class': 'logging.Formatter'
        },
        'access': {
            'format': '%(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S %z',
            'class': 'logging.Formatter'
        },
        'default': {
            'format': '%(asctime)s [%(log_id)s] [%(module)s:%(lineno)d] [%(levelname)s] %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S %z',
            'class': 'logging.Formatter'
        }
    },
    'filters': {
        'log_id_filter': {
            '()': LogIDFilter,  # 使用我们自定义的 Filter
        },
    },
}

# logging.config.dictConfig(DEFAULT_LOGGING)


class Tee(object):
    def __init__(self, out, logger):
        self.out = out
        self.logger = logger

    def write(self, obj):
        # self.out.write(obj)
        if obj.strip():  # Only log non-empty lines
            self.logger.info(obj.rstrip())

    def flush(self):
        # self.out.flush()
        pass
    
# print(sys.stdout)
# try:
#     sys.stdout = Tee(sys.stdout, logging.getLogger('root'))
# except Exception as e:
#     print(e)

# def setup_tee():
#     # sys.stdout = Tee(sys.stdout, logging.getLogger('root'))
#     try:
#         sys.stdout = Tee(sys.stdout, logging.getLogger('root'))
#     except Exception as e:
#         print(e)