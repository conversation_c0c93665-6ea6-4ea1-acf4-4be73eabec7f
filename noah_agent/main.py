import asyncio
import json
import uuid
import traceback
import openai
from dotenv import load_dotenv
from typing import Coroutine

import os
import sys
import time

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, BASE_DIR)
load_dotenv()

from llm.gcp_models import ClaudeSonnet37, <PERSON>Sonnet37Thinking
from llm.deepseek_models import Composite<PERSON>eepseekChat
from agent.human_in_loop.tools import PlanningTools
from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse, StreamingResponse
from utils.catalyst.retrieve import get_catalyst_list, get_catalysts_and_related_info_by_id, get_company_catalysts_and_related_info
from core import lifespan
from workflows.drug_compete import drug_compete
from utils.sql_client import get_connection
from workflows.conf_paper_search import search_conference_papers
from workflows.clinical_trial_design import clinical_trial_design
from workflows.clinical_trial_result_comparison import compare_clinical_trial_results
from structs import ClinicalTrialDesignCom<PERSON>ison, RunPromptTest
from test_prompt import prepare_testset, run_testset
from agent.router import agent_routing
from agent.core.preset import AgentPreset
from tools.general.helper import turncate_dict
import logging.config
from logging_config import LOGGING_CONFIG, log_id_var
# from utils.bio_quant.adapter import run_bio_quant_analysis

logging.config.dictConfig(LOGGING_CONFIG)

logger = logging.getLogger(__name__)
app = FastAPI(lifespan=lifespan)


@app.get("/ping")
def read_root():
    return {"Hello": "World"}

@app.get("/error")
def raise_error():
    raise Exception("Test error")

@app.post('/design_comparison')
async def design_comparison_api(body: ClinicalTrialDesignComparison):
    try:
        res = await clinical_trial_design(**dict(body))
        return JSONResponse(res)
    except Exception as e:
        return JSONResponse({"error": str(e)}, status_code=400)
    
    
@app.post('/clinical_results')
async def result_comparison_api(body: dict):
    try:
        res = await compare_clinical_trial_results(**body)
        return JSONResponse(res)
    except Exception as e:
        traceback.print_exc()
        return JSONResponse({"error": str(e)}, status_code=400) 
    
@app.post('/drug_compete')
async def drug_compete_api(body: dict):
    try:
        res = await drug_compete(**body)
        return JSONResponse(res)
    except Exception as e:
        traceback.print_exc()
        return JSONResponse({"error": str(e)}, status_code=400) 
    
@app.post('/search_conference_papers')
def search_conference_papers_api(body: dict):
    try:
        res = search_conference_papers(**body)
        return JSONResponse(res)
    except Exception as e:
        traceback.print_exc()
        return JSONResponse({"error": str(e)}, status_code=400)

# Used when listing catalysts
@app.post('/catalyst_list')
def get_catalyst_list_api(body: dict):
    try:
        catalyst_id = body.pop('id', None)
        company = body.pop('company', None)
        if catalyst_id:
            body['catalyst_id'] = catalyst_id
        if company:
            body['focus_company'] = [company]
        top_n = body.pop('top_n', 10)
        page = body.pop('page', 1)
        res = get_catalyst_list(top_n=top_n, page=page, get_count=True, details=True, custom_impact=True, **body)
        return JSONResponse(res)
    except Exception as e:
        traceback.print_exc()
        return JSONResponse({"error": str(e)}, status_code=400)

# Used when referencing catalyst    
@app.post('/catalyst_info')
async def catalyst_info_api(body: dict):
    try:
        ids = []
        id_keys = ['id', 'catalyst_id', 'ids']
        
        filters = body.get('filters', {})
        if filters:
            body = filters
        for key in id_keys:
            if key in body:
                ids = body[key]
                break
        res = await get_catalysts_and_related_info_by_id(ids)
        return JSONResponse(res)
    except Exception as e:
        traceback.print_exc()
        return JSONResponse({"error": str(e)}, status_code=400)

@app.post('/run_prompt_playground_test')
async def test_playground_prompt(body: RunPromptTest):
    try:
        if not body.select_fields:
            res = await run_testset(testset=body.testset, prompt=body.prompt)
        else:
            testset = prepare_testset(nctids=body.nctids, select_fields=body.select_fields)
            res = await run_testset(testset=testset, prompt=body.prompt)
        return JSONResponse(json.dumps(res, ensure_ascii=False))
    except openai.BadRequestError as e:
        traceback.print_exc()
        return JSONResponse({"error": str(e.message)}, status_code=400)
    except Exception as e:
        traceback.print_exc()
        return JSONResponse({"error": str(e)}, status_code=400) 
    
@app.post('/chat')
async def chat_api(body: dict):
    try:
        for k,v in list(body.items()):
            if k in ['hitl_mode', 'approve', 'model']:
                continue
            if k != "user_prompt" and not v:
                body.pop(k)
        agent_name = body.pop('agent', None)
        # print("body", body)
        if not agent_name:
            return JSONResponse({"error": "Agent name is required"}, status_code=400)
        if agent_name == 'investment_report':
            symbol = body['user_prompt'].strip()
            agent: AgentPreset = agent_routing[agent_name](symbol=symbol, test=(True if symbol=='NOAH' else False), **body)
            return StreamingResponse(agent.start(**body), media_type='text/event-stream')
        if 'user_prompt' in body:
            if body['user_prompt']:
                body['user_prompt'] = body['user_prompt'].strip()
            if 'symbol' in body or body['user_prompt'].startswith('/IRG '):
                agent_name = 'investment_report'
                symbol = body.pop('symbol', body['user_prompt'][5:].strip())
                agent: AgentPreset = agent_routing[agent_name](symbol=symbol, test=(True if symbol=='NOAH' else False), **body)
                return StreamingResponse(agent.start(**body), media_type='text/event-stream')
            sig = ''
            for option in ['/SG ', '/SGDATA ', '/SGPART ']:
                if body['user_prompt'].startswith(option):
                    sig = option
                    break
            if sig:
                agent_name = 'synopsis_v2'
                query_params_raw = body['user_prompt'][len(sig):].strip().split('&')
                query_params = {}
                for param in query_params_raw:
                    param = param.split('=')
                    if len(param) == 2:
                        key, value = param
                        query_params[key] = value
                gemini_mode = True if sig == '/GSG ' else False
                query_mode = True if sig == '/SGDATA ' else False
                oneshot = True if sig == '/SGPART ' else False 
                print("query_params", query_params)
                agent: AgentPreset = agent_routing[agent_name](query_params=query_params, query_mode=query_mode, oneshot=oneshot, gemini_mode=gemini_mode, **body)
                return StreamingResponse(agent.start(**body), media_type='text/event-stream')
        agent: AgentPreset = agent_routing[agent_name](**body)
        return StreamingResponse(agent.start(**body), media_type='text/event-stream')
    except KeyError as e:
        traceback.print_exc()
        return JSONResponse({"error": str(e)}, status_code=400) 
    except Exception as e:
        traceback.print_exc()
        return JSONResponse({"error": str(e)}, status_code=400) 
    
@app.post('/test')
async def test_api(body: dict):
    try:
        if body['agent'] == 'test':
            async def number_stream():
                for i in range(1, 61):
                    yield f"data: {i}\n\n"
                    await asyncio.sleep(1)
            return StreamingResponse(number_stream(), media_type='text/event-stream')
    except Exception as e:
        traceback.print_exc()
        return JSONResponse({"error": str(e)}, status_code=400)
    
@app.get('/conn')
async def conn_api():
    try:
        get_connection()
        return JSONResponse({"result": "success"})
    except Exception as e:
        traceback.print_exc()
        return JSONResponse({"error": str(e)}, status_code=400)
    
@app.post('/quant')
async def quant_api(body: dict):
    try:
        print(body)
        # res = run_bio_quant_analysis(**body)
        res = {"result":"Not implemented"}
        return JSONResponse(json.dumps(res, ensure_ascii=False))
    except Exception as e:
        traceback.print_exc()
        return JSONResponse({"error": str(e)}, status_code=400)

# Used when generating investment report
@app.post('/catalyst_by_company')
async def catalyst_by_company_api(body: dict):
    try:
        print(body)
        catalyst_info, company_name = await get_company_catalysts_and_related_info(body["ticker"], include_past=body.get('include_past',False))
        return JSONResponse({"name":company_name, "data": catalyst_info})
    except Exception as e:
        traceback.print_exc()
        return JSONResponse({"error": str(e)}, status_code=400)
    
@app.post('/claude')
async def claude(body: dict):
    try:
        print(body)
        if 'thinking' in body and body['thinking']:
            llm = ClaudeSonnet37Thinking()
        else:
            llm = ClaudeSonnet37()
        temperature = body.get('temperature', 1)
        # llm = CompositeDeepseekChat()
        # llm = ClaudeSonnet37()
        user_prompt = body.get('user_prompt', "Hi")
        system_prompt = body.get('system_prompt', "")
        gen = llm.stream_call(sys_prompt=system_prompt, user_prompt=user_prompt, temperature=temperature)
        return StreamingResponse(gen, media_type='text/event-stream')
    except Exception as e:
        traceback.print_exc()
        return JSONResponse({"error": str(e)}, status_code=400)
    
@app.post('/tool_test')
async def tool_test(body: dict):
    try:
        print(body)
        agent = PlanningTools(**body)
        gen = agent.run()
        # if type(gen) == Coroutine:
        #     return JSONResponse({"result": "success", "data": await gen})
        # return StreamingResponse(gen, media_type='text/event-stream')
        
        return JSONResponse({"result": "success", "data": await gen})
    except Exception as e:
        traceback.print_exc()
        return JSONResponse({"error": str(e)}, status_code=400)


@app.middleware("http")
async def log_id_middleware(request: Request, call_next):
    log_id = request.headers.get("X-Correlation-ID") or request.headers.get("x-correlation-id") or str(uuid.uuid4())
    log_id_var.set(log_id)
    logger.info(f"Request started: {request.method} {request.url}")

    # 打印 headers
    logger.info(f"Request headers: {dict(request.headers)}")
    # 打印路径参数
    logger.info(f"Request path params: {request.path_params}")
    # 打印查询参数
    logger.info(f"Request query params: {dict(request.query_params)}")

    start_time = time.time()
    content_type = request.headers.get("content-type", "").lower()
    method = request.method.upper()
    # 只对有 body 的方法处理 body/form
    if method in ("POST", "PUT", "PATCH", "DELETE"):
        if "multipart/form-data" in content_type:
            try:
                form = await request.form()
                fields = {k: v.filename if hasattr(v, 'filename') else v for k, v in form.items()}
                logger.info(f"Request form fields: {fields}")
            except Exception as e:
                logger.warning(f"Failed to read form data: {e}")
        elif "application/x-www-form-urlencoded" in content_type:
            try:
                form = await request.form()
                logger.info(f"Request urlencoded form fields: {dict(form)}")
            except Exception as e:
                logger.warning(f"Failed to read urlencoded form data: {e}")
        elif "application/json" in content_type:
            try:
                body = await request.body()
                logger.info(f"Request JSON body: {body.decode('utf-8')}")
            except Exception as e:
                logger.warning(f"Failed to read JSON body: {e}")
        else:
            try:
                body = await request.body()
                logger.info(f"Request raw body: {body}")
            except Exception as e:
                logger.warning(f"Failed to read raw body: {e}")

    try:
        response = await call_next(request)
        duration = time.time() - start_time
        logger.info(f"Request completed: {response.status_code}, duration: {duration:.3f}s")
        response.headers["X-Correlation-ID"] = log_id
        return response
    except Exception as e:
        logger.error(f"Request failed: {str(e)}")
        raise

@app.middleware("http")
async def request_logging_middleware(request: Request, call_next):
    # 打印 headers
    logger.info(f"Request headers: {dict(request.headers)}")
    # 打印路径参数
    logger.info(f"Request path params: {request.path_params}")
    # 打印查询参数
    logger.info(f"Request query params: {dict(request.query_params)}")

    content_type = request.headers.get("content-type", "").lower()
    method = request.method.upper()
    # 只对有 body 的方法处理 body/form
    if method in ("POST", "PUT", "PATCH", "DELETE"):
        if "multipart/form-data" in content_type:
            try:
                form = await request.form()
                fields = {k: v.filename if hasattr(v, 'filename') else v for k, v in form.items()}
                logger.info(f"Request form fields: {fields}")
            except Exception as e:
                logger.warning(f"Failed to read form data: {e}")
        elif "application/x-www-form-urlencoded" in content_type:
            try:
                form = await request.form()
                logger.info(f"Request urlencoded form fields: {dict(form)}")
            except Exception as e:
                logger.warning(f"Failed to read urlencoded form data: {e}")
        elif "application/json" in content_type:
            try:
                body = await request.body()
                try:
                    import json
                    body_json = json.loads(body)
                    logger.info(f"Request JSON body: {turncate_dict(body_json)}")
                except Exception:
                    logger.info(f"Request JSON body (raw): {body}")
            except Exception as e:
                logger.warning(f"Failed to read JSON body: {e}")
        else:
            try:
                body = await request.body()
                logger.info(f"Request raw body: {body}")
            except Exception as e:
                logger.warning(f"Failed to read raw body: {e}")
    response = await call_next(request)
    return response
