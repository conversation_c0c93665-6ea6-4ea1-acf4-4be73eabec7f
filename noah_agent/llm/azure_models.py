import io
import os
import time
import logging
import datetime

from abc import ABC
from typing import List, Optional
from collections import defaultdict

import httpx
from openai import AsyncAzureOpenAI, AzureOpenAI
from tenacity import retry, stop_after_attempt, wait_random_exponential

from config import api_config
from logging_config import log_id_var
from llm.base_model import BaseLLM, CompositeModel
from tools.core.base_tool import BaseTool

logger = logging.getLogger(__name__)
granular_timeout = httpx.Timeout(45, connect=10.0)
low_timeout_0_retry = {"timeout": granular_timeout, "max_retries": 0}

class OpenAIModel(BaseLLM):
    """
    A base class for model req.
    """
    
    provider = "openai"

    def __init__(self, model, **kwargs) -> None:
        self.model = model
        super().__init__(**kwargs)

    # @retry(wait=wait_random_exponential(multiplier=1, max=3), stop=stop_after_attempt(3))
    async def __call__(self, sys_prompt: str = "", user_prompt: str = "", json_mode: bool = False, temperature: float = 0.1, max_tokens: int = 8192, **kwargs) -> str:
        """
        Asynchronously call the OpenAI API to generate a response.

        Args:
            sys_prompt (str): The system prompt, defaults to an empty string.
            user_prompt (str): The user prompt, defaults to an empty string.
            json_mode (bool): Whether to enable JSON mode, defaults to False.
            temperature (float): The randomness of the generation, defaults to 0.1.
            **kwargs: Additional parameters to pass to the API.

        Returns:
            str: The content of the generated response from the API.
        """
        call_start_time = datetime.datetime.now()
        if json_mode:
            kwargs["response_format"] = {"type": "json_object"}
        if len(kwargs.pop("images", [])) > 0:
            user_message = [{"role": "user", 
                             "content": [
                                 {"type": "text", "text": user_prompt}, 
                                 ] + [{"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}} for base64_image in kwargs.pop("images")]}]
        else:
            user_message = [{"role": "user", "content": user_prompt}]
        history_messages = kwargs.pop('history_messages') if 'history_messages' in kwargs else []
        sys_message = [{"role": "system", "content": sys_prompt}] if sys_prompt else []
        messages = sys_message + history_messages + user_message
        if hasattr(self, 'timeout') and self.timeout:
            kwargs['timeout'] = self.timeout
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
                **kwargs,                
            )
        except Exception as e:
            await self.log_results(sys_prompt, user_prompt, e,
            e, "error", call_start_time)
            raise e
        await self.log_results(sys_prompt, user_prompt, response,
            response.choices[0].message, f"Model: {self.model}, Temperature: {temperature}, Usage: {response.usage}", call_start_time)

        return response.choices[0].message

    async def stream_call(self, sys_prompt: str = "", user_prompt: str = "", temperature: float = 0.1, max_tokens: int = 8192, **kwargs):
        call_start_time = datetime.datetime.now()
        if len(kwargs.pop("images", [])) > 0:
            user_message = [{"role": "user", 
                             "content": [
                                 {"type": "text", "text": user_prompt}, 
                                 ] + [{"type": "image_url", "image_url": {"url": image64}} for image64 in kwargs.pop("images")]}]
        else:
            user_message = [{"role": "user", "content": user_prompt}]
        history_messages = kwargs.pop('history_messages') if 'history_messages' in kwargs else []
        sys_message = [{"role": "system", "content": sys_prompt}] if sys_prompt else []
        messages = sys_message + history_messages + user_message
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                stream=True,
                max_tokens=max_tokens,
                stream_options={"include_usage": True},
                **kwargs
            )
        except Exception as e:
            await self.log_results(sys_prompt, user_prompt, e,
                                   "error", call_start_time)
            raise e
        string_buffer = io.StringIO()
        usage = defaultdict(int)
        async for chunk in response:
            if hasattr(chunk, 'usage') and chunk.usage:
                usage = chunk.usage
            if len(chunk.choices) > 0:
                 chunk_content = chunk.choices[0].delta.content
                 if chunk_content is not None:
                     string_buffer.write(chunk_content)
                     yield chunk_content
            if self.stream_break:
                break
        await response.close()
        self.stream_break = False
        content = string_buffer.getvalue()
        string_buffer.close()
        await self.log_results(sys_prompt, user_prompt, response,
                         content, f"Model: {self.model}, Temperature: {temperature}, Usage: {usage}", call_start_time)

    async def log_results(self, sys_prompt: str, user_prompt: str, response, content: str, usage: str, start_time = None) -> None:
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        end_time = datetime.datetime.now()
        if not os.path.exists("logs"):
            os.makedirs("logs")
        date = datetime.datetime.now().strftime("%Y-%m-%d")
        log_id = log_id_var.get()
        with open(f"logs/open_api_{date}.log", "a", encoding="utf-8") as log_file:
            log_file.write(f"[{log_id}] [{current_time}] {sys_prompt}\n")
            log_file.write(f"[{log_id}] [{current_time}] {user_prompt}\n")
            log_file.write(f"[{log_id}] [{current_time}] {content}\n{usage}\n")
            try:
                log_file.write(f"[{log_id}] Model: {response.model}\n")
            except:
                pass
            if start_time:
                time_delta = end_time - start_time
                formatted_time_delta = f"{time_delta.total_seconds():.2f} seconds"
                log_file.write(f"[{log_id}] Time spent: {formatted_time_delta}\n")
            log_file.write("="*64+"\n")
        with open(f"logs/open_api_usage_{date}.log", "a", encoding="utf-8") as log_file:
            time_delta = f"[{log_id}] [{time_delta.total_seconds():.2f}s]" if start_time else ''
            log_file.write(f"[{log_id}] [{current_time}]{time_delta} {usage}\n")
            log_file.write("="*64+"\n")


class OpenAIReasoningModel(OpenAIModel):
    """
    Openai reasoning model
    """

    provider = "openai"

    async def __call__(self, sys_prompt: str = "", user_prompt: str = "", json_mode: bool = False, temperature: float = 0.1, **kwargs) -> str:
        """
        Asynchronously call the OpenAI API to generate a response.

        Args:
            sys_prompt (str): The system prompt, defaults to an empty string.
            user_prompt (str): The user prompt, defaults to an empty string.
            json_mode (bool): Whether to enable JSON mode, defaults to False.
            temperature (float): The randomness of the generation, defaults to 0.1.
            **kwargs: Additional parameters to pass to the API.

        Returns:
            str: The content of the generated response from the API.
        """
        call_start_time = datetime.datetime.now()
        if json_mode:
            kwargs["response_format"] = {"type": "json_object"}
        kwargs.pop('backup_llms', None)
        kwargs.pop('max_tokens', None)
        if len(kwargs.pop("images", [])) > 0:
            user_message = [{"role": "user", 
                             "content": [
                                 {"type": "text", "text": user_prompt}, 
                                 ] + [{"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}} for base64_image in kwargs.pop("images")]}]
        else:
            user_message = [{"role": "user", "content": user_prompt}]
        history_messages = kwargs.pop('history_messages') if 'history_messages' in kwargs else []
        sys_message = [{"role": "developer", "content": sys_prompt}] if sys_prompt else []
        messages = sys_message + history_messages + user_message
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                reasoning_effort=self.reasoning_effort,
                messages=messages,
                #temperature=temperature,
                max_completion_tokens=8192,
                **kwargs
            )
        except Exception as e:
            await self.log_results(sys_prompt, user_prompt, None,
            e, "error", call_start_time)
            raise e
        await self.log_results(sys_prompt, user_prompt, response,
            response.choices[0].message, f"Model: {self.model}, Temperature: {temperature}, Usage: {response.usage}", call_start_time)
        
        #print(response.model_dump_json(indent=2))
        return response.choices[0].message

    async def stream_call(self, sys_prompt: str = "", user_prompt: str = "", temperature=0.1, **kwargs):
        call_start_time = datetime.datetime.now()
        if len(kwargs.pop("images", [])) > 0:
            user_message = [{"role": "user", 
                             "content": [
                                 {"type": "text", "text": user_prompt}, 
                                 ] + [{"type": "image_url", "image_url": {"url": image64}} for image64 in kwargs.pop("images")]}]
        else:
            user_message = [{"role": "user", "content": user_prompt}]
        sys_message = [{"role": "developer", "content": sys_prompt}] if sys_prompt else []
        history_messages = kwargs.pop('history_messages') if 'history_messages' in kwargs else []
        messages = sys_message + history_messages + user_message
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                stream=True,
                max_completion_tokens=8192,
                stream_options={"include_usage": True},
                **kwargs
            )
        except Exception as e:
            await self.log_results(sys_prompt, user_prompt, None,
                                   str(e), f"Model: {self.model}", start_time=call_start_time)
            raise e
        string_buffer = io.StringIO()
        usage = defaultdict(int)
        async for chunk in response:
            if hasattr(chunk, 'usage') and chunk.usage:
                usage = chunk.usage
            if len(chunk.choices) > 0:
                chunk_content = chunk.choices[0].delta.content
                if chunk_content is not None:
                    string_buffer.write(chunk_content)
                    yield chunk_content
            if self.stream_break:
                break
        await response.close()
        self.stream_break = False
        content = string_buffer.getvalue()
        string_buffer.close()
        await self.log_results(sys_prompt, user_prompt, response,
                         content, f"Model: {self.model}, Temperature: {temperature}, Usage: {usage}", call_start_time)


class AsyncOpenAIClientSingletonMixin:
    """
    @summary: async openai 单例
    """
    _instances: dict[str, AsyncAzureOpenAI] = dict()

    @classmethod
    def make_key(cls, api_key, api_version, azure_endpoint, **kwargs):
        return api_key, api_version, azure_endpoint

    @classmethod
    def get_client(cls, api_key, api_version, azure_endpoint, **kwargs) -> AsyncAzureOpenAI:
        key = cls.make_key(api_key, api_version, azure_endpoint, **kwargs)
        if key not in cls._instances:
            cls._instances[key] = cls.initialize(api_key, api_version, azure_endpoint, **kwargs)
        return cls._instances[key]
    
    @classmethod
    def initialize(cls, api_key, api_version, azure_endpoint, **kwargs) -> None:
        key = cls.make_key(api_key, api_version, azure_endpoint, **kwargs)
        if key not in cls._instances:
            cls._instances[key] = AsyncAzureOpenAI(
                api_key=api_key,
                api_version=api_version,
                azure_endpoint=azure_endpoint,
                **kwargs
            )
        return cls._instances[key]

    @classmethod
    async def cleanup(cls, api_key, api_version, azure_endpoint, **kwargs) -> None:
        key = cls.make_key(api_key, api_version, azure_endpoint, **kwargs)
        if key in cls._instances:
            await cls._instances[key].close()
            del cls._instances[key]

class OpenAIClientSingletonMixin:
    """
    @summary: openai 单例
    """
    _instances: dict[str, AzureOpenAI] = dict()

    @classmethod
    def make_key(cls, api_key, api_version, azure_endpoint, **kwargs):
        return (api_key, api_version, azure_endpoint) + tuple(sorted(kwargs.items()))

    @classmethod
    def get_client(cls, api_key, api_version, azure_endpoint, **kwargs) -> AzureOpenAI:
        key = cls.make_key(api_key, api_version, azure_endpoint, **kwargs)
        if key not in cls._instances:
            cls._instances[key] = cls.initialize(api_key, api_version, azure_endpoint, **kwargs)
        return cls._instances[key]
    
    @classmethod
    def initialize(cls, api_key, api_version, azure_endpoint, **kwargs) -> None:
        key = cls.make_key(api_key, api_version, azure_endpoint, **kwargs)
        if key not in cls._instances:
            cls._instances[key] = AzureOpenAI(
                api_key=api_key,
                api_version=api_version,
                azure_endpoint=azure_endpoint,
                **kwargs
            )
        return cls._instances[key]

    @classmethod
    def cleanup(cls, api_key, api_version, azure_endpoint, **kwargs) -> None:
        key = cls.make_key(api_key, api_version, azure_endpoint, **kwargs)
        if key in cls._instances:
            cls._instances[key].close()
            del cls._instances[key]

class GPT4o(OpenAIModel, AsyncOpenAIClientSingletonMixin):

    def __init__(self) -> None:
        client = AsyncOpenAIClientSingletonMixin.get_client(
            api_key=api_config.AZURE_GPT4_OPENAI_API_KEY,
            api_version=api_config.AZURE_GPT4_AGENT_OPENAI_API_VERSION,
            azure_endpoint=api_config.AZURE_GPT4_AZURE_ENDPOINT,
            max_retries=5
        )

        self.client = client
        super().__init__(model=api_config.AZURE_GPT4_AZURE_DEPLOYMENT)
    
    async def call_response(self, sys_prompt: str = "", user_prompt: str = "", json_mode: bool = False, temperature: float = 0.1, **kwargs) -> str:
        call_start_time = datetime.datetime.now()
        if json_mode:
            kwargs["response_format"] = {"type": "json_object"}
        if 'json' not in user_prompt:
            user_prompt += '\nPlease output in json'
        if "images" in kwargs:
            user_message = [{"role": "user", 
                             "content": [
                                 {"type": "text", "text": user_prompt}, 
                                 ] + [{"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}} for base64_image in kwargs.pop("images")]}]
        else:
            user_message = [{"role": "user", "content": user_prompt}]
            
        history_messages = kwargs.pop('history_messages') if 'history_messages' in kwargs else []
        sys_message = [{"role": "system", "content": sys_prompt}] if sys_prompt else []
        messages = sys_message + history_messages + user_message
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=4096,
                
                **kwargs
            )
        except Exception as e:
            await self.log_results(sys_prompt, user_prompt, e,
                                   "error", call_start_time)
            raise e
        await self.log_results(sys_prompt, user_prompt, response,
            response.choices[0].message, response.usage, call_start_time)

        return response
    
class GPT4oMini(OpenAIModel, AsyncOpenAIClientSingletonMixin):

    def __init__(self) -> None:
        client = AsyncOpenAIClientSingletonMixin.get_client(
            api_key=api_config.AZURE_GPT4_OPENAI_API_KEY,
            api_version=api_config.AUZRE_GPT4o_MIN_VERSION,
            azure_endpoint=api_config.AZURE_GPT4_AZURE_ENDPOINT
        )
        self.client = client
        super().__init__(model=api_config.AUZRE_GPT4o_MIN_AZURE_DEPLOYMENT)

class GPTo1(OpenAIReasoningModel, AsyncOpenAIClientSingletonMixin):

    def __init__(self, reasoning_effort: str="medium") -> None:
        client = AsyncOpenAIClientSingletonMixin.get_client(
            api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
            api_version=api_config.AZURE_GPTo1_VERSION,
            azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
            **low_timeout_0_retry
        )
        self.client = client
        self.reasoning_effort  = reasoning_effort
        super().__init__(model=api_config.AZURE_GPTo1_DEPLOYMENT)

class GPTo1Mini(OpenAIReasoningModel, AsyncOpenAIClientSingletonMixin):

    def __init__(self, reasoning_effort: str="high") -> None:
        client = AsyncOpenAIClientSingletonMixin.get_client(
            api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
            api_version=api_config.AZURE_GPTo1_MINI_VERSION,
            azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
            **low_timeout_0_retry
        )
        self.client = client
        self.reasoning_effort  = reasoning_effort
        super().__init__(model=api_config.AZURE_GPTo1_MINI_DEPLOYMENT)

class GPTo3Mini(OpenAIReasoningModel, AsyncOpenAIClientSingletonMixin):

    def __init__(self, reasoning_effort: str="high") -> None:
        client = AsyncOpenAIClientSingletonMixin.get_client(
            api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
            api_version=api_config.AZURE_GPTo3_MIN_VERSION,
            azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
            **low_timeout_0_retry
        )
        self.client = client
        self.reasoning_effort  = reasoning_effort
        super().__init__(model=api_config.AZURE_GPTo3_MINI_DEPLOYMENT)

class GPTo3(OpenAIReasoningModel, AsyncOpenAIClientSingletonMixin):

    def __init__(self, reasoning_effort: str="medium") -> None:
        client = AsyncOpenAIClientSingletonMixin.get_client(
            api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
            api_version=api_config.AZURE_GPTo3_VERSION,
            azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
            **low_timeout_0_retry
        )
        self.client = client
        self.reasoning_effort  = reasoning_effort
        super().__init__(model=api_config.AZURE_GPTo3_DEPLOYMENT)

class GPTo4Mini(OpenAIReasoningModel, AsyncOpenAIClientSingletonMixin):

    def __init__(self, reasoning_effort: str="high", timeout: Optional[float]=None) -> None:
        client = AsyncOpenAIClientSingletonMixin.get_client(
            api_key=api_config.AZURE_GPTo4_MIN_API_KEY,
            api_version=api_config.AZURE_GPTo4_MIN_VERSION,
            azure_endpoint=api_config.AZURE_GPTo4_MIN_ENDPOINT,
            **low_timeout_0_retry
        )
        self.client = client
        self.reasoning_effort  = reasoning_effort
        self.timeout = timeout
        super().__init__(model=api_config.AZURE_GPTo4_MIN_DEPLOYMENT)

class GPT41(OpenAIModel, AsyncOpenAIClientSingletonMixin):

    def __init__(self) -> None:
        client = AsyncOpenAIClientSingletonMixin.get_client(
            api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
            api_version=api_config.AZURE_GPT4_1_VERSION,
            azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT, 
            **low_timeout_0_retry
        )
        self.client = client
        super().__init__(model=api_config.AZURE_GPT4_1_DEPLOYMENT)

class GPT4oWorkflow(OpenAIModel, AsyncOpenAIClientSingletonMixin):

    def __init__(self) -> None:
        client = AsyncOpenAIClientSingletonMixin.get_client(
            api_key=api_config.AZURE_GPT4_OPENAI_API_KEY,
            api_version=api_config.AZURE_GPT4_OPENAI_API_VERSION,
            azure_endpoint=api_config.AZURE_GPT4_AZURE_ENDPOINT,
            **low_timeout_0_retry
        )
        self.client = client
        super().__init__(model=api_config.AZURE_GPT4_AZURE_DEPLOYMENT)


class Ada(OpenAIModel, OpenAIClientSingletonMixin):

    def __init__(self) -> None:
        client = OpenAIClientSingletonMixin.get_client(
            api_key=api_config.AZURE_ADA002_OPENAI_API_KEY,
            api_version=api_config.AZURE_ADA002_OPENAI_API_VERSION,
            azure_endpoint=api_config.AZURE_ADA002_AZURE_ENDPOINT,
            azure_deployment=api_config.AZURE_ADA002_AZURE_DEPLOYMENT
        )
        self.client = client
        self.provider = "openai"
        super().__init__(model=api_config.AZURE_ADA002_MODEL)

    def get_embedding(self, text: str) -> list[float]:
        """
        Get the embedding vector for the given text.

        Args:
            text (str): The text to be embedded.

        Returns:
            list[float]: The embedding vector of the text.
        """
        response = self.client.embeddings.create(
            model=self.model,
            input=text
        )
        return response.data[0].embedding

class Compositeo4mini(CompositeModel):
    provider = "openai"
    
    models = [GPTo4Mini(reasoning_effort='medium', timeout=60), GPT41(), GPTo4Mini(reasoning_effort='low', timeout=55), GPT41()]
    
    
class DiagnosisModels(CompositeModel):
    def __init__(self, **kwargs) -> None:
        self.models = [GPTo3(), GPTo4Mini(), GPTo3Mini(), GPTo1(), GPT41()]
        super().__init__()
