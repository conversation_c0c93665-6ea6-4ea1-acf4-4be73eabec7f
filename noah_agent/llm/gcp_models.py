import io
import os
import asyncio
import datetime
import logging

import httpx
import openai

from collections import defaultdict
from anthropic import AsyncAnthropicVertex
from google import auth
from google.auth.transport.requests import Request
from typing import Optional

from logging_config import log_id_var, task_id_var
from config import api_config
from llm.base_model import BaseLLM
from llm.azure_models import OpenAIModel, GPT41
from utils.metadata import SingletonMeta

logger = logging.getLogger(__name__)

creds, project = auth.default(scopes=['https://www.googleapis.com/auth/cloud-platform'])
auth_req = Request()
PROJECT = api_config.VERTEX_PROJECT_ID
LOCATION = 'us-central1'

class GeminiClientSingleton:
    _instance: Optional[openai.AsyncOpenAI] = None
    _initialized: bool = False

    @classmethod
    def get_client(cls) -> openai.AsyncOpenAI:
        if cls._instance is None:
            cls.initialize()
        return cls._instance
    
    @classmethod
    def initialize(cls) -> None:
        if not cls._initialized:
            creds.refresh(auth_req)
            cls._instance = openai.AsyncOpenAI(
                base_url=f'https://{LOCATION}-aiplatform.googleapis.com/v1/projects/{PROJECT}/locations/{LOCATION}/endpoints/openapi',
                api_key=creds.token
            )
            cls._initialized = True

    @classmethod
    async def cleanup(cls) -> None:
        if cls._instance:
            await cls._instance.close()
            cls._instance = None
        cls._initialized = False

    @classmethod
    def refresh_credentials(cls) -> None:
        """Refresh credentials and recreate client"""
        if cls._instance:
            # Note: We can't await close() here in a sync method, 
            # but we'll recreate the instance which should handle cleanup
            creds.refresh(auth_req)
            cls._instance = openai.AsyncOpenAI(
                base_url=f'https://{LOCATION}-aiplatform.googleapis.com/v1/projects/{PROJECT}/locations/{LOCATION}/endpoints/openapi',
                api_key=creds.token
            )
# Backward compatibility - remove this later
GEMINI_CLIENT = None  # Will be deprecated

class VertexClaudeModel(BaseLLM):

    max_tokens = 32000

    """
    A base class for model req.
    """
    def __init__(self, region:str, project_id:str, model:str) -> None:
        self.client = AsyncAnthropicVertex(region=region,  project_id=project_id, timeout=30, max_retries=0)
        self.model = model
    
    async def __call__(self, sys_prompt: str = "", user_prompt: str = "", json_mode: bool = False, temperature: float = 0.5, **kwargs) -> str:
        call_start_time = datetime.datetime.now()
        if len(kwargs.pop("images", [])) > 0:
            user_message = [{"role": "user", 
                             "content": [
                                 {"type": "text", "text": user_prompt}, 
                                 ] + [{"type": "image_url", "image_url": {"url": image64}} for image64 in kwargs.pop("images")]}]
        else:
            user_message = [{"role": "user", "content": user_prompt}]
        history_messages = kwargs.pop('history_messages', []) if isinstance(kwargs.get('history_messages'), list) else []
        messages = history_messages + user_message
        if sys_prompt:
            kwargs["system"] = sys_prompt
        response = await self.client.messages.create(
            model=self.model,
            messages=messages,
            temperature=temperature,
            max_tokens=self.max_tokens,
            **kwargs
        )
        print(response)
        await self.log_results(sys_prompt, user_prompt, response.content[0], "vertex-placeholder", call_start_time)
        return response.content
        
    async def stream_call(self, sys_prompt: str = "", user_prompt: str = "", temperature: float = 0.5, **kwargs):
        try:
            call_start_time = datetime.datetime.now()
            if len(kwargs.pop("images", [])) > 0:
                user_message = [{"role": "user", 
                                "content": [
                                    {"type": "text", "text": user_prompt}, 
                                    ] + [{"type": "image_url", "image_url": {"url": image64}} for image64 in kwargs.pop("images")]}]
            else:
                user_message = [{"role": "user", "content": user_prompt}]
            history_messages = kwargs.pop('history_messages', []) if isinstance(kwargs.get('history_messages'), list) else []
            messages = history_messages + user_message
            for key in ['system_prompt', 'stream', 'stream_status']:
                kwargs.pop(key, None)
            if sys_prompt:
                kwargs["system"] = sys_prompt
            
            """
            response = await asyncio.wait_for(self.client.messages.create(
                model=self.model,
                messages=messages,
                system=sys_prompt,
                temperature=temperature,
                stream=True,
                max_tokens=4000,
                **kwargs
            ), timeout=5)
            """
            logger.info(f"Claude call using temperature: {temperature}")
            response = await self.client.messages.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                stream=True,
                max_tokens=self.max_tokens,
                **kwargs
            )
            string_buffer = io.StringIO()
            usage = defaultdict(int)
        
            async for chunk in response:
                if hasattr(chunk, 'message') and hasattr(chunk.message, 'usage') and chunk.message.usage:
                    for key, value in chunk.message.usage.__dict__.items():
                        if type(value) == int:
                            usage[key] += value
                if hasattr(chunk, 'usage') and chunk.usage:
                    for key, value in chunk.usage.__dict__.items():
                        if type(value) == int:
                            usage[key] += value
                if chunk.type == 'content_block_delta':
                    chunk_content = chunk.delta.text
                    string_buffer.write(chunk_content)
                    yield chunk_content
                if self.stream_break:
                    break
            await response.close()
            self.stream_break = False
            content = string_buffer.getvalue()
            string_buffer.close()
            await self.log_results(sys_prompt, user_prompt, content, f"Model: {self.model}, Temperature: {temperature}, Usage: {usage}\n", call_start_time)
            if not usage.get('output_tokens', 0):
                raise Exception("No output tokens in usage, likely an error in the request or response.")
        except Exception as e:
            logger.error(f"Error in stream_call: {e}")
            raise e
        
    async def log_results(self, sys_prompt:str, user_prompt:str, content: str, usage: str, start_time=None) -> None:
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        end_time = datetime.datetime.now()
        if not os.path.exists("logs"):
            os.makedirs("logs")
        date = datetime.datetime.now().strftime("%Y-%m-%d")
        log_id = log_id_var.get()
        task_id = task_id_var.get()
        with open(f"logs/open_api_{date}.log", "a", encoding="utf-8") as log_file:
            log_file.write(f"[{log_id}] [{current_time}] {sys_prompt}\n")
            log_file.write(f"[{log_id}] [{current_time}] {user_prompt}\n")
            log_file.write(f"[{log_id}] [{current_time}] {content}\n{usage}\n")
            if start_time:
                time_delta = end_time - start_time
                formatted_time_delta = f"{time_delta.total_seconds():.2f} seconds"
                log_file.write(f"[{log_id}] Time spent: {formatted_time_delta}\n")
            log_file.write("="*64+"\n")
        with open(f"logs/open_api_usage_{date}.log", "a", encoding="utf-8") as log_file:
            time_delta = f"[{time_delta.total_seconds():.2f}s]" if start_time else ''
            log_file.write(f"[{log_id}] [{current_time}][{task_id}] {time_delta} {usage}\n")

    async def generate_stream(self, user_prompt, **kwargs):
        print("generate_stream", self.__class__.__name__)
        async for chunk in self.stream_call(user_prompt=user_prompt, **kwargs):
            yield chunk

class VertexClaudeThikingModel(VertexClaudeModel):
    
    async def stream_call(self, sys_prompt: str = "", user_prompt: str = "", temperature: float = 0.5, **kwargs):
        try:
            call_start_time = datetime.datetime.now()
            if sys_prompt:
                kwargs["system"] = sys_prompt
            if len(kwargs.pop("images", [])) > 0:
                user_message = [{"role": "user", 
                                "content": [
                                    {"type": "text", "text": user_prompt}, 
                                    ] + [{"type": "image_url", "image_url": {"url": image64}} for image64 in kwargs.pop("images")]}]
            else:
                user_message = [{"role": "user", "content": user_prompt}]
            history_messages = kwargs.pop('history_messages', []) if isinstance(kwargs.get('history_messages'), list) else []
            messages = history_messages + user_message
            
            response = await self.client.messages.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                stream=True,
                max_tokens=self.max_tokens,
                thinking={
                    "type": "enabled",
                    "budget_tokens": 5 * 1024
                    },
                **kwargs
            )
            reasoning_flag = False
            string_buffer = io.StringIO()
            usage = defaultdict(int)
            async for chunk in response:
                if hasattr(chunk, 'message') and hasattr(chunk.message, 'usage') and chunk.message.usage:
                    for key, value in chunk.message.usage.__dict__.items():
                        if type(value) == int:
                            usage[key] += value
                if hasattr(chunk, 'usage') and chunk.usage:
                    for key, value in chunk.usage.__dict__.items():
                        if type(value) == int:
                            usage[key] += value
                if chunk.type == 'content_block_delta':

                    if chunk.delta.type == 'thinking_delta':
                    
                        if not reasoning_flag:
                            reasoning_flag = True
                            chunk_content = f"<think>\n{chunk.delta.thinking}"
                        else:
                            chunk_content = chunk.delta.thinking
                    
                    elif chunk.delta.type == 'text_delta':

                        if reasoning_flag:
                            reasoning_flag = False
                            chunk_content = f"</think>\n{chunk.delta.text}"
                        else:
                            chunk_content = chunk.delta.text
                    
                    if chunk_content:
                        string_buffer.write(chunk_content)
                        yield chunk_content
                if self.stream_break:
                    break
            await response.close()
            self.stream_break = False
            content = string_buffer.getvalue()
            string_buffer.close()
            await self.log_results(sys_prompt, user_prompt, content, f"Model: {self.model}, Temperature: {temperature}, Usage: {usage}\n", call_start_time)
            if not usage.get('output_tokens', 0):
                raise Exception("No output tokens in usage, likely an error in the request or response.")
        except Exception as e:
            logger.error(f"Error in stream_call: {e}")
            raise e
                    
class ClaudeSonnet35(VertexClaudeModel, metaclass=SingletonMeta):
    
    provider = "anthropic"
    max_tokens = 8192

    def __init__(self, max_tokens: int = 8192) -> None:
        super().__init__(
            api_config.VERTEX_CLAUDE35_REGION,
            api_config.VERTEX_PROJECT_ID,
            api_config.VERTEX_CLAUDE35_MODEL_ID,
        )
        self.max_tokens = max_tokens

class ClaudeSonnet37(VertexClaudeModel, metaclass=SingletonMeta):
    
    provider = "anthropic"

    def __init__(self, max_tokens: int = 64000) -> None:
        super().__init__(
            api_config.VERTEX_CLAUDE37_REGION,
            api_config.VERTEX_PROJECT_ID,
            api_config.VERTEX_CLAUDE37_MODEL_ID,
        )
        self.max_tokens = max_tokens
        
class ClaudeSonnet4(VertexClaudeModel, metaclass=SingletonMeta):
    provider = "anthropic"

    def __init__(self, max_tokens:int = 32000) -> None:
        super().__init__(
            api_config.VERTEX_CLAUDE37_REGION,
            api_config.VERTEX_PROJECT_ID,
            api_config.VERTEX_CLAUDE4_MODEL_ID,
        )
        self.max_tokens = max_tokens
        
class ClaudeOpus4(VertexClaudeModel, metaclass=SingletonMeta):
    provider = "anthropic"

    def __init__(self, max_tokens:int = 32000) -> None:
        super().__init__(
            "us-east5",
            api_config.VERTEX_PROJECT_ID,
            "claude-opus-4@20250514",
        )
        self.max_tokens = max_tokens

class ClaudeSonnet37Thinking(VertexClaudeThikingModel, metaclass=SingletonMeta):
    
    provider = "anthropic"

    def __init__(self, max_tokens: int = 64000) -> None:
        super().__init__(
            api_config.VERTEX_CLAUDE37_REGION,
            api_config.VERTEX_PROJECT_ID,
            api_config.VERTEX_CLAUDE37_MODEL_ID,
        )
        self.max_tokens = max_tokens

class ClaudeSonnet37ThinkingBypass(ClaudeSonnet37Thinking):
    
    provider = "anthropic"

    async def stream_call(self, sys_prompt = "", user_prompt = "", temperature = 0.5, **kwargs):
        data = {"prompt": user_prompt, "thinking": True}
        # gen = llm.stream_call(user_prompt=prompt, temperature=0.5)
        url = 'https://test.noahai.co/api/claude/'
        token = api_config.NOAH_ADMIN_TOKEN
        headers = {'Content-Type': 'application/json', 'Authorization': token}
        # response = requests.post(url, headers=headers, json=data, stream=True)
        async with httpx.AsyncClient() as client:
            async with client.stream('POST', url, headers=headers, json=data, timeout=30) as r:
                async for chunk in r.aiter_text():  # or, for line in r.iter_lines():
                    yield chunk
        

class ClaudeSonnet37Bypass(ClaudeSonnet37):
    
    provider = "anthropic"

    async def stream_call(self, sys_prompt = "", user_prompt = "", temperature = 0.5, **kwargs):
        data = {"prompt": user_prompt}
        # gen = llm.stream_call(user_prompt=prompt, temperature=0.5)
        url = 'https://test.noahai.co/api/claude/'
        token = api_config.NOAH_ADMIN_TOKEN
        headers = {'Content-Type': 'application/json', 'Authorization': token}
        # response = requests.post(url, headers=headers, json=data, stream=True)
        async with httpx.AsyncClient() as client:
            async with client.stream('POST', url, headers=headers, json=data, timeout=30) as r:
                async for chunk in r.aiter_text():  # or, for line in r.iter_lines():
                    yield chunk
        
class VertexGeminiModel(OpenAIModel):
    def __init__(self, *args, **kwargs) -> None:
        self.client = GeminiClientSingleton.get_client()
        super().__init__(*args, **kwargs)
        
    async def stream_call(self, sys_prompt: str = "", user_prompt: str = "", temperature: float = 0.1, max_tokens: int = 32000, **kwargs):
        call_start_time = datetime.datetime.now()
        if len(kwargs.pop("images", [])) > 0:
            user_message = [{"role": "user", 
                             "content": [
                                 {"type": "text", "text": user_prompt}, 
                                 ] + [{"type": "image_url", "image_url": {"url": image64}} for image64 in kwargs.pop("images")]}]
        else:
            user_message = [{"role": "user", "content": user_prompt}]
        history_messages = kwargs.pop('history_messages') if 'history_messages' in kwargs else []
        sys_message = [{"role": "system", "content": sys_prompt}] if sys_prompt else []
        messages = sys_message + history_messages + user_message
        try:
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                stream=True,
                max_tokens=max_tokens,
                **kwargs
            )
        except openai.AuthenticationError as e:
            if e.response.status_code == 401:  # Unauthorized
            # Refresh credentials and retry
                logger.info("Gemini authentication error, refreshing credentials and retrying...")
                GeminiClientSingleton.refresh_credentials()
                self.client = GeminiClientSingleton.get_client()
                response = await self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=temperature,
                    stream=True,
                    max_tokens=max_tokens,
                    **kwargs
                )
        except Exception as e:
            await self.log_results(sys_prompt, user_prompt, e,
                                   "error", call_start_time)
            raise e
        string_buffer = io.StringIO()
        usage = defaultdict(int)
        async for chunk in response:
            if hasattr(chunk, 'usage') and chunk.usage:
                usage = chunk.usage
                for key, value in usage.__dict__.items():
                    if type(value) == 'int':
                        usage[key] += value
            if len(chunk.choices) > 0:
                 choice = chunk.choices[0]
                 if choice.delta is not None and choice.delta.content is not None:
                     chunk_content = choice.delta.content
                     string_buffer.write(chunk_content)
                     yield chunk_content
        content = string_buffer.getvalue()
        string_buffer.close()
        await self.log_results(sys_prompt, user_prompt, response,
                         content, f"Model: {self.model}, Temperature: {temperature}, Usage: {usage}", call_start_time)

    async def generate_stream(self, user_prompt, **kwargs):
        async for chunk in super().stream_call(user_prompt=user_prompt):
            yield chunk
        
class Gemini15Flash(VertexGeminiModel):
    def __init__(self) -> None:
        super().__init__(model=f"google/{api_config.VERTEX_GEMINI15_FLASH_MODEL_ID}")

class Gemini15Pro(VertexGeminiModel):
    def __init__(self) -> None:
        super().__init__(model=f"google/{api_config.VERTEX_GEMINI15_PRO_MODEL_ID}")

class Gemini20Flash(VertexGeminiModel):
    def __init__(self) -> None:
        super().__init__(model=f"google/{api_config.VERTEX_GEMINI20_FLASH_MODEL_ID}")

class Gemini20Pro(VertexGeminiModel):
    def __init__(self) -> None:
        super().__init__(model=f"google/{api_config.VERTEX_GEMINI20_FLASH_MODEL_ID}")

class Gemini25Pro(VertexGeminiModel):
    def __init__(self) -> None:
        super().__init__(model=f"google/{api_config.VERTEX_GEMINI25_PRO_MODEL_ID}")

class Gemini25Flash(VertexGeminiModel):
    def __init__(self) -> None:
        super().__init__(model=f"google/{api_config.VERTEX_GEMINI25_FLASH_MODEL_ID}")


class CompositeClaude(VertexClaudeModel):

    models = [ClaudeSonnet4(), ClaudeSonnet37(), GPT41(), ClaudeSonnet35(), GPT41()]

    def __init__(self) -> None:
        self.current_index = 0
        self.loop_count = 5
        self.current_model = self.models[self.current_index]
        self.provider = self.current_model.provider if hasattr(self.current_model, 'provider') else ''

    def _try_next_model(self):
        r"""Try to switch to the next available model in the chain"""
        if self.current_index >= len(self.models) - 1 and len(self.models) and self.loop_count > 0:
            self.loop_count -= 1
            self.current_index = 0
        if self.current_index < len(self.models) - 1:
            self.current_index += 1
            self.current_model = self.models[self.current_index]
            self.provider = self.current_model.provider if hasattr(self.current_model, 'provider') else ''
            logger.info(f"Switching to {self.models.__class__.__name__}...")
            return True
        return False

    async def __call__(self, **kwargs) -> str:
        last_error = None
        while True:
            try:
                res = await self.current_model.__call__(**kwargs)
                self.current_index = 0  # Reset index on success
                self.current_model = self.models[self.current_index]
                self.loop_count = 5  # Reset loop count on success
                return res
            except Exception as e:
                last_error = e
                if not self._try_next_model():
                    raise RuntimeError(f"All models in chain failed. Last error: {str(last_error)}")
                
    async def stream_call(self, timeout: float = 15.0, **kwargs):
        last_error = None
        while True:
            try:
                async for chunk in self.current_model.stream_call(**kwargs):
                    yield chunk
                self.current_index = 0  # Reset index on success
                self.current_model = self.models[self.current_index]
                self.loop_count = 5  # Reset loop count on success
                return
            except asyncio.TimeoutError:
                logger.warn(f"Stream call timeout after {timeout} seconds")
                last_error = f"Timeout after {timeout} seconds"
                if not self._try_next_model():
                    raise RuntimeError(f"All models in chain failed. Last error: {last_error}")
            except Exception as e:
                logger.warn(f"Error: {str(e)}")
                logger.warn("Retrying with next model...")
                last_error = e
                if not self._try_next_model():
                    raise RuntimeError(f"All models in chain failed. Last error: {str(last_error)}")


class CompositeClaudeChat(CompositeClaude):

    provider = "anthropic"

    models = [ClaudeSonnet4(), ClaudeSonnet37(), GPT41(), ClaudeSonnet35(), GPT41()]

