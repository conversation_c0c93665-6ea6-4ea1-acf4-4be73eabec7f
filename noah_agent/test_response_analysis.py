#!/usr/bin/env python3
"""
详细分析MindSearchResponse为什么没有code_graph的问题
"""
import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def analyze_response_types():
    """分析不同Agent返回的响应类型"""
    
    print("🔍 分析响应类型问题")
    print("=" * 80)
    
    try:
        # 1. 测试MindSearchFinanceHitlAgent
        print("\n📊 1. 测试MindSearchFinanceHitlAgent")
        print("-" * 50)
        
        from agent.explore.mindsearch_agent_v2 import MindSearchFinanceHitlAgent
        
        finance_agent = MindSearchFinanceHitlAgent()
        print(f"Agent类型: {type(finance_agent)}")
        print(f"Final Output Agent类型: {type(finance_agent.final_output_agent)}")
        print(f"Helper类型: {type(finance_agent.helper)}")
        
        # 测试init_response方法
        response = finance_agent.helper.init_response(finance_agent)
        print(f"\n📋 Helper.init_response()返回:")
        print(f"   类型: {type(response)}")
        print(f"   字段: {list(response.__dict__.keys())}")
        
        # 检查字段
        if hasattr(response, 'search_graph'):
            print("   ✅ 有search_graph字段")
            print(f"   search_graph类型: {type(response.search_graph)}")
        else:
            print("   ❌ 没有search_graph字段")
            
        if hasattr(response, 'code_graph'):
            print("   ✅ 有code_graph字段")
            print(f"   code_graph类型: {type(response.code_graph)}")
        else:
            print("   ❌ 没有code_graph字段")
        
        # 2. 测试SimpleCoderAgent
        print("\n📊 2. 测试SimpleCoderAgent")
        print("-" * 50)
        
        from agent.coder.simple_coder_agent import SimpleCoderAgent
        
        coder_agent = SimpleCoderAgent()
        print(f"Agent类型: {type(coder_agent)}")
        
        # 测试init_response方法
        coder_response = coder_agent.helper.init_response(coder_agent)
        print(f"\n📋 Helper.init_response()返回:")
        print(f"   类型: {type(coder_response)}")
        print(f"   字段: {list(coder_response.__dict__.keys())}")
        
        # 检查字段
        if hasattr(coder_response, 'search_graph'):
            print("   ✅ 有search_graph字段")
            print(f"   search_graph类型: {type(coder_response.search_graph)}")
        else:
            print("   ❌ 没有search_graph字段")
            
        if hasattr(coder_response, 'code_graph'):
            print("   ✅ 有code_graph字段")
            print(f"   code_graph类型: {type(coder_response.code_graph)}")
        else:
            print("   ❌ 没有code_graph字段")
        
        # 3. 分析问题原因
        print("\n🔍 3. 问题原因分析")
        print("-" * 50)
        
        print("📝 关键发现:")
        print("   1. MindSearchFinanceHitlAgent 继承自 MindSearchAgentV2")
        print("   2. MindSearchAgentV2.use_tool() 调用 helper.init_response()")
        print("   3. helper.init_response() 根据agent类型返回不同响应")
        print("   4. 但是MindSearchFinanceHitlAgent被识别为MindSearch类型，不是Coder类型")
        
        # 4. 测试实际的use_tool调用
        print("\n📊 4. 测试实际use_tool调用")
        print("-" * 50)
        
        print("🚀 调用MindSearchFinanceHitlAgent.use_tool()...")
        
        response_count = 0
        code_response_found = False
        mind_search_response_found = False
        
        async for chunk in finance_agent.use_tool(
            user_prompt="1+1等于几",
            language="CN"
        ):
            response_count += 1
            
            if hasattr(chunk, 'code_graph'):
                code_response_found = True
                print(f"   📦 响应#{response_count}: CodeResponse - ✅")
                print(f"      code_graph: {chunk.code_graph}")
            elif hasattr(chunk, 'search_graph'):
                mind_search_response_found = True
                print(f"   📦 响应#{response_count}: MindSearchResponse - ✅")
                print(f"      search_graph: {chunk.search_graph}")
            else:
                print(f"   📦 响应#{response_count}: {type(chunk)}")
            
            # 限制测试数量
            if response_count >= 5:
                print("   ⏹️ 达到测试限制，停止测试")
                break
        
        print(f"\n📊 测试结果:")
        print(f"   总响应数: {response_count}")
        print(f"   检测到CodeResponse: {'✅' if code_response_found else '❌'}")
        print(f"   检测到MindSearchResponse: {'✅' if mind_search_response_found else '❌'}")
        
        # 5. 解决方案建议
        print("\n💡 5. 解决方案建议")
        print("-" * 50)
        
        if not code_response_found:
            print("❌ 问题确认: MindSearchFinanceHitlAgent 不返回 CodeResponse")
            print("\n🔧 解决方案:")
            print("   方案1: 修改 MindSearchHelper.init_response() 方法")
            print("   方案2: 创建专门的 CoderFinanceAgent 类")
            print("   方案3: 修改 MindSearchFinanceHitlAgent 的响应处理逻辑")
            
            print("\n📝 推荐方案: 方案1 - 修改 helper.init_response()")
            print("   原因: 最小化代码修改，保持向后兼容")
        else:
            print("✅ 没有问题: CodeResponse 正常返回")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_helper_logic():
    """测试Helper的逻辑"""
    
    print("\n🔍 测试Helper逻辑")
    print("=" * 80)
    
    try:
        from agent.explore.helper import MindSearchHelper
        from agent.explore.mindsearch_agent_v2 import MindSearchFinanceHitlAgent
        from agent.coder.simple_coder_agent import SimpleCoderAgent
        
        helper = MindSearchHelper()
        
        # 测试不同类型的agent
        agents = [
            ("MindSearchFinanceHitlAgent", MindSearchFinanceHitlAgent()),
            ("SimpleCoderAgent", SimpleCoderAgent()),
            ("字符串", "test_string"),
        ]
        
        for name, agent in agents:
            print(f"\n📊 测试 {name}:")
            response = helper.init_response(agent)
            print(f"   返回类型: {type(response)}")
            print(f"   有code_graph: {hasattr(response, 'code_graph')}")
            print(f"   有search_graph: {hasattr(response, 'search_graph')}")
            
            if hasattr(agent, '__class__'):
                print(f"   Agent类名: {agent.__class__.__name__}")
                print(f"   包含'Coder': {'Coder' in agent.__class__.__name__}")
                print(f"   包含'Code': {'Code' in agent.__class__.__name__}")
        
    except Exception as e:
        print(f"❌ Helper测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    await analyze_response_types()
    await test_helper_logic()

if __name__ == "__main__":
    asyncio.run(main())
