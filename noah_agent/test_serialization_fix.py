#!/usr/bin/env python3
"""
测试序列化修复的脚本
验证MindSearchFinanceHitlAgent和SimpleCoderAgent的序列化问题是否已解决
"""
import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_coder_agent_serialization():
    """测试SimpleCoderAgent的序列化"""
    print("🧪 测试SimpleCoderAgent序列化...")
    
    try:
        from agent.coder.simple_coder_agent import SimpleCoderAgent
        
        # 创建实例
        coder = SimpleCoderAgent(
            enable_file_management=False,
            enable_code_review=False,
            use_sandbox=True,
            sandbox_url="http://0.0.0.0:8194"
        )
        
        print("✅ SimpleCoderAgent实例创建成功")
        
        # 测试基本属性访问
        print(f"📊 工具数量: {len(coder.tools)}")
        print(f"🏖️  沙箱URL: {coder.sandbox_url}")
        print(f"🔧 使用沙箱: {coder.use_sandbox}")
        
        # 尝试序列化（这是之前出错的地方）
        try:
            # 注意：不能直接序列化Agent对象，但可以序列化其配置
            config = {
                "sandbox_url": coder.sandbox_url,
                "use_sandbox": coder.use_sandbox,
                "work_dir": coder.work_dir,
                "tools_count": len(coder.tools)
            }
            json_str = json.dumps(config)
            print("✅ 配置序列化成功")
            
        except Exception as e:
            print(f"❌ 序列化失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ SimpleCoderAgent测试失败: {e}")
        return False


def test_finance_hitl_agent_serialization():
    """测试MindSearchFinanceHitlAgent的序列化"""
    print("\n🧪 测试MindSearchFinanceHitlAgent序列化...")
    
    try:
        from agent.explore.mindsearch_agent_v2 import MindSearchFinanceHitlAgent
        
        # 创建实例
        finance_agent = MindSearchFinanceHitlAgent()
        
        print("✅ MindSearchFinanceHitlAgent实例创建成功")
        
        # 检查final_output_agent
        print(f"📊 Final Output Agent类型: {type(finance_agent.final_output_agent)}")
        
        # 检查是否有coder_agent属性
        if hasattr(finance_agent.final_output_agent, 'coder_agent'):
            print("✅ 检测到coder_agent属性")
            
            # 检查coder_agent是否为None（延迟初始化）
            if finance_agent.final_output_agent.coder_agent is None:
                print("✅ coder_agent正确设置为None（延迟初始化）")
                
                # 测试延迟初始化
                finance_agent.final_output_agent._ensure_coder_agent()
                if finance_agent.final_output_agent.coder_agent is not None:
                    print("✅ 延迟初始化成功")
                else:
                    print("❌ 延迟初始化失败")
                    return False
            else:
                print("⚠️  coder_agent不是None，可能存在序列化风险")
        else:
            print("❌ 未检测到coder_agent属性")
            return False
        
        # 测试基本配置序列化
        try:
            config = {
                "agent_type": type(finance_agent).__name__,
                "has_coder_agent": hasattr(finance_agent.final_output_agent, 'coder_agent'),
                "coder_agent_initialized": finance_agent.final_output_agent.coder_agent is not None
            }
            json_str = json.dumps(config)
            print("✅ 配置序列化成功")
            
        except Exception as e:
            print(f"❌ 序列化失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ MindSearchFinanceHitlAgent测试失败: {e}")
        return False


def test_code_response_serialization():
    """测试CodeResponse的序列化"""
    print("\n🧪 测试CodeResponse序列化...")
    
    try:
        from agent.explore.schema import CodeResponse, CodeNode, CodeType, CodeSubject, ProcessingType
        
        # 创建CodeResponse实例
        response = CodeResponse(
            processing_type=ProcessingType.PROCESSING,
            code_graph=CodeNode(
                code_type=CodeType.UNKNOWN,
                query="测试查询",
                subject=CodeSubject.CODE,
                processing_type=ProcessingType.PROCESSING
            )
        )
        
        print("✅ CodeResponse实例创建成功")
        
        # 测试序列化
        try:
            # 使用Pydantic的model_dump方法
            data = response.model_dump()
            json_str = json.dumps(data)
            print("✅ CodeResponse序列化成功")
            
            # 测试反序列化
            parsed_data = json.loads(json_str)
            restored_response = CodeResponse(**parsed_data)
            print("✅ CodeResponse反序列化成功")
            
        except Exception as e:
            print(f"❌ CodeResponse序列化失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ CodeResponse测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始序列化修复验证测试")
    print("=" * 60)
    
    # 运行各项测试
    tests = [
        ("SimpleCoderAgent序列化", test_simple_coder_agent_serialization),
        ("MindSearchFinanceHitlAgent序列化", test_finance_hitl_agent_serialization),
        ("CodeResponse序列化", test_code_response_serialization),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有序列化问题已修复!")
        return True
    else:
        print("⚠️  仍有序列化问题需要解决")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)


# ================================
# 修复说明
# ================================

"""
🔧 序列化问题的修复说明：

1. 问题根源：
   - SimpleCoderAgent中混用了SearchType和CodeType
   - MindSearchWithCoderFinalOutputAgent在类定义时直接实例化SimpleCoderAgent
   - 这导致序列化时类型不匹配和循环引用问题

2. 修复方案：
   - 修正了SimpleCoderAgent中的类型使用（SearchType -> CodeType）
   - 将MindSearchWithCoderFinalOutputAgent中的coder_agent改为延迟初始化
   - 添加_ensure_coder_agent方法来按需创建coder_agent实例

3. 关键修改：
   - simple_coder_agent.py: 修正了所有类型不匹配问题
   - mindsearch_agent_v2.py: 实现了延迟初始化模式

4. 验证方法：
   - 运行此测试脚本验证修复效果
   - 运行test_explore.py确认原有功能正常
   - 检查序列化和反序列化是否正常工作

📝 使用建议：
   - 在生产环境中使用前，确保所有测试通过
   - 监控序列化性能，确保延迟初始化不影响响应时间
   - 定期运行此测试脚本验证序列化稳定性
"""
