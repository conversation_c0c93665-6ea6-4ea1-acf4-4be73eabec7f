import re
import copy
import time
import json
import logging

from typing import List, Callable, Any
from datetime import datetime
from urllib.parse import quote
from openai.types.chat import ChatCompletionMessage

import agent.explore.constants as constants
from agent.core.preset import AgentPreset
from agent.explore.mindsearch_agent import MindSearchAgent
from agent.explore.schema import (MindSearchResponse, SearchNode, SearchType, WebSearchLink,
                                  ProcessingType, WebSearchSubject, WebSearchRegion,
                                  SearchEngine)
from agent.explore.mindsearch_prompt_v2 import (gpt_query_rewrite_sys_pt, gpt_query_rewrite_user_pt,
                                                gpt_web_qr_sys_pt, gpt_medical_qr_sys_pt,
                                                gpt_patent_qr_sys_pt, gpt_news_qr_sys_pt,
                                                gpt_finance_qr_sys_pt,
                                                ds_search_final_output_sys_pt, gpt_o_search_final_output_sys_pt,
                                                ds_search_final_output_user_pt,
                                                gpt_search_final_output_sys_pt)
from agent.explore.prompt import (rag_r1_final_output_pt, r1_refer_norag_pt,
                                  norag_final_output_pt)
from llm.composite_models import Compositeo4mini, CompositeHitlFinal, CompositeMindsearchFinal, Compositeo3
from llm.base_model import BaseLLM
from llm.azure_models import GPT41
from llm.gcp_models import Gemini25Flash, Gemini25Pro
from llm.deepseek_models import CompositeDeepseekChat
from tools.core.base_tool import BaseTool
from tools.explore.mindsearch_tools_v2 import (GeneralSearch, MedicalSearch, NewsSearch,
                                               PatentSearch, PubmedArticlesSearch, StockHistoricalPriceQuery,
                                               CompanyPressReleasesNewsQuery, CompanyInfoQuery, FinancialStatements,
                                               StockGeneralSearch, StockNewsSearch, ChinaCompanyFinancialStatements,
                                               WebpageReader, Finished, DatastoreFinished)
from tools.explore.workflow_tools import (CatalystEventsDatabaseQuery, ClinicalResultsDatabaseQuery, DrugCompetitionDatabaseQuery)
from tools.explore.calculate_tools import Calculate
from utils.scholar import SCIIF
from utils.web_search import ContentFetcher
from utils.tokenizer import tokenizer
from agent.coder.simple_coder_agent import SimpleCoderAgent

logger = logging.getLogger(__name__)


class MindSearchQueryRewriteAgent(AgentPreset):
    llm: BaseLLM = Compositeo4mini
    sys_prompt: str = ''
    tools: List[BaseTool] = [
        GeneralSearch,
        MedicalSearch,
        NewsSearch,
        PatentSearch,
        PubmedArticlesSearch,
        StockHistoricalPriceQuery,
        StockNewsSearch,
        WebpageReader,
        Finished
    ]
    tool_choice: str = "required"


class MindSearchMedicalHitlQueryRewriteAgent(AgentPreset):
    llm: BaseLLM = Compositeo4mini
    sys_prompt: str = ''
    tools: List[BaseTool] = [
        MedicalSearch,
        PubmedArticlesSearch,
        WebpageReader,
        Calculate,
        Finished
    ]
    tool_choice: str = "required"


class MindSearchWebHitlQueryRewriteAgent(AgentPreset):
    llm: BaseLLM = Compositeo4mini
    sys_prompt: str = ''
    tools: List[BaseTool] = [
        GeneralSearch,
        WebpageReader,
        Calculate,
        Finished
    ]
    tool_choice: str = "required"


class MindSearchPatentHitlQueryRewriteAgent(AgentPreset):
    llm: BaseLLM = Compositeo4mini
    sys_prompt: str = ''
    tools: List[BaseTool] = [
        PatentSearch,
        WebpageReader,
        Finished
    ]
    tool_choice: str = "required"


class MindSearchNewsHitlQueryRewriteAgent(AgentPreset):
    llm: BaseLLM = Compositeo4mini
    sys_prompt: str = ''
    tools: List[BaseTool] = [
        NewsSearch,
        WebpageReader,
        Finished
    ]
    tool_choice: str = "required"


class FinanicalModleingPrepAgent(AgentPreset):
    llm: BaseLLM = Compositeo4mini
    sys_prompt: str = ''
    tools: List[BaseTool] = [
        GeneralSearch,
        StockGeneralSearch,
        StockHistoricalPriceQuery,
        StockNewsSearch,
        CompanyPressReleasesNewsQuery,
        CompanyInfoQuery,
        FinancialStatements,
        ChinaCompanyFinancialStatements,
        WebpageReader,
        Calculate,
        Finished,
    ]
    tool_choice: str = "required" 


class MindSearchFinalOutputAgent(AgentPreset):
    llm: BaseLLM = Compositeo3
    sys_prompt: str = gpt_o_search_final_output_sys_pt
    tools: List[BaseTool] = []


class MindSearchWithCoderFinalOutputAgent(MindSearchFinalOutputAgent):
    """带有代码计算能力的通用搜索最终输出智能体"""
    
    llm: BaseLLM = Compositeo3
    sys_prompt: str = gpt_o_search_final_output_sys_pt
    tools: List[BaseTool] = []
    
    coder_agent: SimpleCoderAgent = SimpleCoderAgent(enable_file_management=False, enable_code_review=False)
    
    intent_llm: BaseLLM = Compositeo4mini
    
    async def _detect_computation_intent(self, user_prompt: str, search_results: str) -> dict:
        """检测是否需要复杂计算"""
        print(f"🔍 意图识别 - 分析问题: {user_prompt}")
        
        intent_prompt = f"""
            你是一个意图识别专家。请分析用户问题和搜索结果，判断是否需要复杂计算。

            用户问题：{user_prompt}

            搜索结果摘要：{search_results[:10000]}

            请判断是否需要以下类型的复杂计算：
            1. 数学计算（如收益率计算、财务比率分析、ROE计算）
            2. 数据可视化（如图表生成、趋势分析、K线图）
            3. 算法实现（如投资组合优化、风险评估、技术指标计算）
            4. 统计分析（如相关性分析、回归分析、波动率计算）
            5. 金融建模（如DCF估值、期权定价、债券定价）

            请返回JSON格式：
            {{
                "needs_computation": true/false,
                "computation_type": "数学计算/数据可视化/算法实现/统计分析/金融建模",
                "computation_task": "具体的计算任务描述和任务的参数",
                "reasoning": "判断理由"
            }}

            注意：返回computation_task的时候，需要带任务的具体参数，比如计算5年的利率，需要带5年的数据。
        """
        
        try:
            print("🤖 调用意图识别LLM...")
            # 实例化 LLM
            intent_llm_instance = self.intent_llm()
            response = await intent_llm_instance(user_prompt=intent_prompt)
            
            intent_result = json.loads(response.content)
            print(f"✅ 意图识别完成: {intent_result}")
            logger.info(f"意图识别结果: {intent_result}")
            return intent_result
        except Exception as e:
            print(f"❌ 意图识别失败: {e}")
            logger.warning(f"意图识别失败: {e}")
            return {"needs_computation": False, "reasoning": "意图识别失败"}
    
    async def _execute_computation(self, computation_task: str, search_results: str) -> str:
        """执行复杂计算"""
        print(f"🚀 开始执行复杂计算: {computation_task}")
        
        # 构建代码生成提示
        code_prompt = f"""
            基于以下搜索结果，请生成代码来完成计算任务：

            搜索结果（截断版本）：
            {search_results[:10000] if len(search_results) > 10000 else search_results}

            计算任务：{computation_task}

            请生成Python代码来实现这个计算，并执行它。如果搜索结果中包含相关数据，请使用这些数据；如果没有，请生成示例数据来演示计算过程。
        """
        
        computation_result = ""
        try:
            print("💻 调用代码智能体...")
            logger.info(f"开始执行代码计算: {computation_task}")
            
            chunk_count = 0
            async for chunk in self.coder_agent.use_tool(
                user_prompt=code_prompt,
            ):
                chunk_count += 1
                print(f"📦 代码智能体响应 #{chunk_count}: {type(chunk)}")
                
                if isinstance(chunk, dict) and 'function' in chunk:
                    if chunk.get('function') == 'CodeGeneration':
                        print("💻 检测到代码生成")
                        computation_result += f"\n生成的代码：\n{chunk.get('generated_code', '')}\n"
                    elif chunk.get('function') == 'CodeExecution':
                        print("▶️ 检测到代码执行")
                        result = chunk.get('result', {})
                        chunk['result']['sandbox_response']['data']['stdout']
                        computation_result += f"\n执行结果：\n状态：{result.get('status', '')}\n输出：{result.get('sandbox_response', {}).get("data", {}).get("stdout", "")}\n"
                elif hasattr(chunk, 'content') and chunk.content:
                    print("🤖 检测到LLM输出")
                    computation_result += f"\nLLM输出：{chunk.content}\n"
            
            print(f"✅ 代码计算完成，共处理 {chunk_count} 个响应")
            logger.info(f"代码计算完成: {computation_task}")
            return computation_result
        except Exception as e:
            print(f"❌ 代码执行失败: {e}")
            logger.error(f"代码执行失败: {e}")
            return f"代码执行失败: {str(e)}"
    
    async def use_tool(self, user_prompt: str, history_messages: List[dict] = [], **kwargs):
        """重写use_tool方法，增加意图识别和代码计算"""
        
        print("\n" + "🔍 MindSearchWithCoderFinalOutputAgent.use_tool 开始执行")
        print(f"📝 用户问题: {user_prompt}")
        
        # 1. 首先进行意图识别
        search_results = kwargs.get('search_results', '')
        print(f"🔍 开始意图识别...")
        intent_result = await self._detect_computation_intent(user_prompt, search_results)
        print(f"🎯 意图识别结果: {intent_result}")
        
        # 2. 如果需要复杂计算，先执行计算
        computation_result = ""
        if intent_result.get('needs_computation', False):
            print(f"🧮 检测到需要复杂计算: {intent_result}")
            print(f"📋 计算任务: {intent_result.get('computation_task', '')}")
            
            # 执行计算
            print("🚀 开始执行代码计算...")
            computation_result = await self._execute_computation(
                intent_result.get('computation_task', ''),
                search_results
            )
            print(f"✅ 代码计算完成，结果长度: {len(computation_result)}")
            print(f"📊 计算结果预览: {computation_result}...")
        else:
            print("ℹ️ 未检测到需要复杂计算，直接进行最终输出")
        
        # 3. 调用父类的最终输出逻辑，但使用增强的提示词
        # 构建完整的提示词
        if computation_result:
            # 如果有计算结果，将其加入到最终提示词中
            final_prompt = f"""
{user_prompt}

复杂计算结果：
{computation_result}

请基于搜索结果和上述计算结果，提供完整的分析报告。重点关注：
1. 计算结果的解释和意义
2. 与搜索结果的关联分析
3. 对用户问题的完整回答
"""
            print("📝 已构建增强提示词，包含计算结果")
        else:
            # 如果没有计算结果，使用原始提示词
            final_prompt = user_prompt
            print("📝 使用原始提示词")
        
        print("🔄 调用父类的最终输出逻辑...")
        
        # 过滤掉 coder_agent 参数，避免传递给 LLM
        llm_allowed_keys = {"user_prompt", "history_messages", "search_results", "images"}
        filtered_kwargs = {k: v for k, v in kwargs.items() if k in llm_allowed_keys}

        # 打印传递给 super().use_tool 的参数
        call_params = {
            "user_prompt": final_prompt,
            "history_messages": history_messages,
            **filtered_kwargs
        }

        async for chunk in super().use_tool(
            user_prompt=final_prompt,
            history_messages=history_messages,
            **filtered_kwargs
        ):
            # 检查是否是 Pydantic 对象
            if hasattr(chunk, 'model_dump'):
                yield chunk  # Pydantic 对象可以直接 yield
            elif isinstance(chunk, (dict, list, str)):
                yield chunk  # 基本类型可以直接 yield
            else:
                # 其他类型转换为字符串
                yield str(chunk)
        
        print("✅ MindSearchWithCoderFinalOutputAgent.use_tool 执行完成")

class MindSearchAgentV2(MindSearchAgent):
    llm: BaseLLM = Compositeo4mini

    query_rewrite_agent: MindSearchQueryRewriteAgent = MindSearchQueryRewriteAgent()
    final_output_agent: MindSearchFinalOutputAgent =  MindSearchFinalOutputAgent()
    webpage_fetcher: ContentFetcher = ContentFetcher()
    sci_if_client: SCIIF = SCIIF()
    query_rewrite_rounds: int = 8

    def _format_qr_prompt(
        self,
        history_search: dict,
        query: str,
        background: str = '',
        language: str = constants.ENGLISH) -> str:
        
        user_pt = gpt_query_rewrite_user_pt.format(
            current_date=datetime.now().strftime('%Y-%m-%d'),
            language=language,
            history_search=history_search,
            background=background,
            user_question=query,
        )
        return gpt_query_rewrite_sys_pt + user_pt

    async def _query_rewrite(
        self,
        response: MindSearchResponse,
        runtime_info: dict,
        query: str,
        history_messages: List[dict] = ...,
        background: str = '',
        language: str = constants.ENGLISH):
        
        finished = False

        for _ in range(0, self.query_rewrite_rounds):
            
            # check length
            nsh = self._format_history_action(runtime_info)
            if len(tokenizer.openai(nsh, history_messages)) > 190 * 1000:
                logger.info("chunk size is too big for model, break searching")
                break

            user_prompt = self._format_qr_prompt(nsh, query, background, language)

            node = self._add_thinking_node(response=response, query=query, language=language)

            async for chunk in self.query_rewrite_agent.use_tool(user_prompt=user_prompt, history_messages=history_messages):
                if isinstance(chunk, ChatCompletionMessage):
                    logger.info(f"[_query_rewrite_with_mindsearch] gpt output {chunk}")
                    self._process_chunk(node, chunk, language)
                
                elif isinstance(chunk, dict):    
                    # process fc result
                    runtime_info['history_search'].append(chunk)
                    await self._process_fc_result(chunk, runtime_info, node, language)

                    # break process
                    if chunk.get('function', '') == Finished().name:
                        finished = True
            
            if finished:
                break
    
    def _tool_name_translation(
        self,
        tool_name: str,
        language: str):
        if tool_name == MedicalSearch().name:
            if constants.CHINESE == language:
                return "医学检索"
            elif constants.JAPANESE == language:
                return "医学分野の検索"
            elif constants.ARABIC == language:
                return "بحث في المجال الطبي"
            else:
                return "Health Search"
        elif tool_name == NewsSearch().name:
            if constants.CHINESE == language:
                return "新闻检索"
            elif constants.JAPANESE == language:
                return "ニュース検索"
            elif constants.ARABIC == language:
                return "بحث الأخبار"
            else:
                return "News Search"
        elif tool_name == PatentSearch().name:
            if constants.CHINESE == language:
                return "专利检索"
            elif constants.JAPANESE == language:
                return "特許検索"
            elif constants.ARABIC == language:
                return "بحث براءات الاختراع"
            else:
                return "Patent Search"
        elif tool_name == PubmedArticlesSearch().name:
            if constants.CHINESE == language:
                return "PubMed检索"
            elif constants.JAPANESE == language:
                return "PubMed検索"
            elif constants.ARABIC == language:
                return "بحث في قاعدة PubMed"
            else:
                return "PubMed Search" 
        elif tool_name == StockHistoricalPriceQuery().name:
            if constants.CHINESE == language:
                return "历史股价检索"
            elif constants.JAPANESE == language:
                return "過去株価検索"
            elif constants.ARABIC == language:
                return "بحث أسعار الأسهم التاريخية"
            else:
                return "Historical Stock Price Search"
        elif tool_name == StockNewsSearch().name:
            if constants.CHINESE == language:
                return "股票新闻检索"
            elif constants.JAPANESE == language:
                return "株価ニュース検索"
            elif constants.ARABIC == language:
                return "بحث أخبار الأسهم"
            else:
                return "Stock News Search"
        elif tool_name == CompanyPressReleasesNewsQuery().name:
            if constants.CHINESE == language:
                return "公司公告检索"
            elif constants.JAPANESE == language:
                return "企業公告検索"
            elif constants.ARABIC == language:
                return "بحث إعلانات الشركة"
            else:
                return "Corporate Announcement Search"
        elif tool_name == CompanyInfoQuery().name:
            if constants.CHINESE == language:
                return "公司公告检索"
            elif constants.JAPANESE == language:
                return "企業公告検索"
            elif constants.ARABIC == language:
                return "بحث إعلانات الشركة"
            else:
                return "Corporate Announcement Search"
        elif tool_name in [FinancialStatements().name, ChinaCompanyFinancialStatements().name]:
            if constants.CHINESE == language:
                return "公司财报"
            elif constants.JAPANESE == language:
                return "会社の財務報告書"
            elif constants.ARABIC == language:
                return "التقرير المالي للشركة"
            else:
                return "Company's Financial Statements"
        elif tool_name in [Finished().name, WebpageReader().name]:
            if constants.CHINESE == language:
                return "阅读网页"
            elif constants.JAPANESE == language:
                return "ウェブページを読む"
            elif constants.ARABIC == language:
                return "اقرأ صفحة الويب"
            else:
                return "Read Webpages"
        elif tool_name in [CatalystEventsDatabaseQuery().name, 
                           ClinicalResultsDatabaseQuery().name,
                           DrugCompetitionDatabaseQuery().name]:
            if constants.CHINESE == language:
                return "数据库查询"
            elif constants.JAPANESE == language:
                return "データベースクエリ"
            elif constants.ARABIC == language:
                return "استعلام قاعدة البيانات"
            else:
                return "Database Query"
        elif tool_name == DatastoreFinished().name:
            if constants.CHINESE == language:
                return "筛选数据"
            elif constants.JAPANESE == language:
                return "データフィルタリング"
            elif constants.ARABIC == language:
                return "تصفية البيانات"
            else:
                return "Data Filtering"                        
        else:
            if constants.CHINESE == language:
                return "网络搜索"
            elif constants.JAPANESE == language:
                return "ウェブ検索"
            elif constants.ARABIC == language:
                return "بحث على الإنترنت"
            else:
                return "Web Search"                                   


    def _process_chunk(
        self,
        node: SearchNode,
        response: ChatCompletionMessage,
        language: str = constants.ENGLISH):
        r"""Update step tip."""
        for tool_call in response.tool_calls:
            try:
                args = json.loads(tool_call.function.arguments)
                
                node.query = self._tool_name_translation(tool_call.function.name, language)
                
                if 'thought_process' in args:
                    node.summary += f"{args['thought_process']}"

            except Exception as exc:
                logger.warning(f"[Mindsearch] Try parse llm response function result failed {exc}")

    async def _process_fc_result(
        self,
        result: dict,
        runtime_info: dict,
        node: SearchNode,
        language: str,
    ):
        r"""
        1. update node query
        2. add citation id in web search link
        3. format weblink
        """

        function = result.get('function', '')
        params = result.get('params', {})
        thought_process = params.get('thought_process', '')
        url_content_map = {}

        def format_summary(language: str):
            if constants.CHINESE == language:
                return "任务完成"
            elif constants.JAPANESE == language:
                return "任務完了"
            elif constants.ARABIC == language:
                return "تم إنجاز المهمة"
            else:
                return "Task Completed"
        
        def format_query(language: str, tool_name: str, titles_str: str):
            tool = self._tool_name_translation(tool_name, language)
            if titles_str == '':
                if constants.CHINESE == language:
                    return f"{tool}结束"
                elif constants.JAPANESE == language:
                    return f"{tool}終了"
                elif constants.ARABIC == language:
                    return f"{tool} - تم بنجاح"
                else:
                    return f"{tool} Finished" 
            if constants.CHINESE == language:
                return f"{tool} - {titles_str[:20]}...结束"
            elif constants.JAPANESE == language:
                return f"{tool} - {titles_str[:20]}...終了"
            elif constants.ARABIC == language:
                return f"...{titles_str[:20]} - {tool} - تم بنجاح"
            else:
                return f"{tool} - {titles_str[:20]}... Finished" 
        
        # don't get PubMed webpage, since PubMed webpage content only contains summary
        def get_url_title(urls: list[int], filterout_pubmed: bool = False, filterout_google_patent: bool = False):
            url_title_map = {}
            url_map = runtime_info['url_map']
            for id in urls:
                for url, value in url_map.items():
                    if id == value.id:
                        if filterout_pubmed and value.type == SearchType.PUBMED:
                            continue
                        if filterout_google_patent and value.type == SearchType.PATENT and value.site_name == 'google.com':
                            continue
                        title = url_map[url].title or url_map[url].site_name
                        if title == '':
                            continue
                        url_title_map[url] = title
            return url_title_map
        
        def get_google_patent_id(urls: list[int]):
            google_patent_map = {}
            url_map = runtime_info['url_map']
            for id in urls:
                for url, value in url_map.items():
                    if id == value.id and value.type == SearchType.PATENT \
                        and value.site_name == 'google.com' and value.patent_id != '':
                        google_patent_map[url] = value.patent_id
            return google_patent_map
        
        async def fetch_content(urls: list[int]):
            # Find url related title
            url_title_map = get_url_title(params.get('urls', []))
            nurls = list(get_url_title(params.get('urls', []), filterout_pubmed=True, filterout_google_patent=True).keys())
            titles_str = ",".join(url_title_map.values())
        
            # fetch webpage content
            url_content_map = await self.webpage_fetcher.fetch_urls(nurls, enable_retry=True)

            # fetcn patents
            google_patent_url_map = get_google_patent_id(params.get('urls', []))
            url_patent_map = await self.webpage_fetcher.fetch_google_patents(patent_ids=list(google_patent_url_map.values()))
            for url, patent_id in google_patent_url_map.items():
                content = url_patent_map.get(patent_id, None)
                if content:
                    url_content_map[url] = content
            
            return titles_str, url_content_map, list(url_title_map.items())

        def reading_webpage_summary(language: str, links: list) -> str:
            def headline(language: str):
                if constants.CHINESE == language:
                    return f"### 需要深入阅读链接: "
                elif constants.JAPANESE == language:
                    return f"### 詳細を読むためのリンク: "  
                elif constants.ARABIC == language:
                    return f"### روابط للقراءة المتعمقة: "  
                else:
                    return f"### Key Reference to Read:"

            # [WebSearchLink(url=url, summ='', title=title) for url, title in url_title_map.items()]
            links_str = ("\n").join([
                f"- [{title}]({url})"
                for url, title in links
            ])
            return headline(language) + "\n" + links_str

          
        # Format display node
        # 1. Common search, get queries and search result
        # 2. PubMed search, get query and search resutl
        if function in [MedicalSearch().name, GeneralSearch().name, NewsSearch().name, PatentSearch().name]:
            def keyword(language: str):
                if constants.CHINESE == language:
                    return "### 搜索词:"
                elif constants.JAPANESE == language:
                    return "### 検索語:"
                elif constants.ARABIC == language:
                    return "### كلمة البحث"
                else:
                    return "### Search Keywords:"

            # Merge query
            query = "\n".join([
                sub_query.get('key_word', '') if language != constants.ENGLISH else sub_query.get('key_word_en', '')
                for sub_query in result.get('sub_queries', [])
            ])
            # Merge summary 
            summary = keyword(language) + "\n" + "\n".join([
                f"- {sub_query.get('sub_query', '')}"
                for sub_query in result.get('sub_queries', [])
            ])
            # Format type
            type = SearchType.WEB
            if function == PatentSearch.__name__:
                type = SearchType.PATENT
            elif function == NewsSearch.__name__:
                type = SearchType.NEWS
            # Add search results
            for sub_query in result.get('sub_queries', []):
                for value in sub_query.get('search_result', {}).values():
                    node.add_search_result(self._format_websearch_weblink(value, type))

            node.query = format_query(language, function, query)
            node.search_type = SearchType.WEB
            node.summary = f"{thought_process}\n{summary}"
        
        elif PubmedArticlesSearch().name == function:
            def format_query(language: str):
                if language == 'Chinese':
                    return "PubMed学术文献搜索"
                elif language == 'Japanese':
                    return "PubMed学術文献検索"
                elif language == 'Arabic':
                    return "البحث عن الأدبيات الأكاديمية"
                else:
                    return "PubMed Search"
            
            # Add search results
            for value in result.get("result", []):
                link = self._format_pubmed_weblink(value)
                node.add_search_result(link)
                # To avoid give frontend too much data, we keep PubMed articles' summariese in url_content_map
                url_content_map[link.url] = value.get('summary', '')
            
            node.key_word = params.get('pubmed_query', '')
            node.search_type = SearchType.PUBMED
            node.query = format_query(language=language)
            node.summary = f"{thought_process}\n\n**PubMed Search Term:** {node.key_word}"

        elif StockHistoricalPriceQuery().name == function:
            symbol = params.get('symbol', '')
            date_from = params.get('date_from', '')
            date_to = params.get('date_to', '')
            
            def format_query(language: str):
                if constants.CHINESE == language:
                    return f"获取{symbol}历史股价{date_from} ~ {date_to}"
                elif constants.JAPANESE == language:
                    return f"過去{symbol}の株価を取得する {date_from} ~ {date_to}"
                elif constants.ARABIC == language:
                    return f"الحصول على أسعار الأسهم التاريخية {date_from} ~ {date_to}"
                else:
                    return f"Get {symbol} Historical Stock Prices {date_from} ~ {date_to}" 

            node.query = format_query(language=language)
            node.summary = f"{thought_process}\n\n" + self.format_stockprice_summary(result.get('result', {}))
            #logger.info(node.summary)

        elif StockNewsSearch().name == function:
            symbol = params.get('symbol', '')
            date_from = params.get('date_from', '')
            date_to = params.get('date_to', '')
            def format_query(language: str):
                if constants.CHINESE == language:
                    return f"获取{symbol} {date_from} ~ {date_to}最新新闻"
                elif constants.JAPANESE == language:
                    return f"{symbol} {date_from} ~ {date_to}の最新ニュースを取得する"
                elif constants.ARABIC == language:
                    return f"الحصول على أحدث أخبار {symbol} {date_from} ~ {date_to}"
                else:
                    return f"Get Latest News of {symbol} {date_from} ~ {date_to}"
            
            def format_link(link: dict):
                return WebSearchLink(
                    url=link.get('url', ''),
                    title=link.get('title', ''),
                    summ=link.get('text', ''),
                    type=SearchType.NEWS
                )
            
            node.query = format_query(language=language)
            node.search_results = [format_link(link) for link in result.get('result', []) if link.get('url', '') != '']
            node.summary = thought_process

        elif CompanyPressReleasesNewsQuery().name == function:
            symbol = params.get('symbol', '')
            def format_query(language: str):
                if constants.CHINESE == language:
                    return f"获取{symbol}发布公告"
                elif constants.JAPANESE == language:
                    return f"{symbol}の発表を取得する"
                elif constants.ARABIC == language:
                    return f"الحصول على إعلانات {symbol}"
                else:
                    return f"Get Announcements of {symbol}"  
            
            node.query = format_query(language=language)
            node.search_results = [format_link(link) for link in result.get('result', []) if link.get('url', '') != '']
            node.summary = thought_process

        elif CompanyInfoQuery().name == function:
            symbol = params.get('symbol', '')
            def format_query(language: str):
                if constants.CHINESE == language:
                    return f"获取{symbol}公司信息"
                elif constants.JAPANESE == language:
                    return f"{symbol}の企業情報を取得する"
                elif constants.ARABIC == language:
                    return f"الحصول على معلومات الشركة {symbol}"
                else:
                    return f"Get Company Information of {symbol}"
            
            node.query = format_query(language=language)
            node.summary = thought_process
        
        elif function in [FinancialStatements().name, ChinaCompanyFinancialStatements().name] :
            symbol = params.get('symbol', '')

            def format_query(language: str):
                if constants.CHINESE == language:
                    return f"获取{symbol}公司财报信息"
                elif constants.JAPANESE == language:
                    return f"{symbol}社の財務報告情報を取得する"
                elif constants.ARABIC == language:
                    return f"احصل على معلومات التقرير المالي لشركة {symbol}"
                else:
                    return f"Retrieve {symbol} Financial Statements"
            
            node.query = format_query(language=language)
            node.summary = thought_process
        
        # TODO to delete later
        elif function in [CatalystEventsDatabaseQuery().name,
                          ClinicalResultsDatabaseQuery().name,
                          DrugCompetitionDatabaseQuery().name]:
            # format jumplink
            params = result.get('function_params', {})
            if function == CatalystEventsDatabaseQuery().name:
                url = f"/tool/catalyst/?search_param={quote(json.dumps(params))}"
            elif function == ClinicalResultsDatabaseQuery().name:
                url = f"/tool/clinical-result/?search_param={quote(json.dumps(params))}"
            else:
                url = f"/tool/drug-compete/?search_param={quote(json.dumps(params))}"

            def format_summary(language: str):
                if constants.CHINESE == language:
                    return f"数据预览"
                elif constants.JAPANESE == language:
                    return f"データプレビュー"
                elif constants.ARABIC == language:
                    return f"معاينة البيانات"
                else:
                    return f"Data Preview"

            def summary_count(language: str):
                if constants.CHINESE == language:
                    return f"数据条数"
                elif constants.JAPANESE == language:
                    return f"データ件数"
                elif constants.ARABIC == language:
                    return f"عدد البيانات"
                else:
                    return f"Data Count"                             
            
            # add result count
            count = len(result.get('result', {}).get('results', []))
            total_count = result.get('result', {}).get('total_count', 0)

            node.query = format_query(language=language, tool_name=function, titles_str='')
            node.summary = f"{thought_process}\n - [{format_summary(language)}]({url}) \n - {summary_count(language)}: {count}"

        elif WebpageReader().name == function:
            titles_str, url_content_map, url_items = await fetch_content(params.get('urls', []))

            node.query = format_query(language=language, tool_name=function, titles_str=titles_str)         
            node.summary = thought_process + "\n" + reading_webpage_summary(language, url_items)
        
        elif Finished().name == function:
            def format_query(language: str):
                if constants.CHINESE == language:
                    return f"搜索结束"
                elif constants.JAPANESE == language:
                    return f"検索終了"
                elif constants.ARABIC == language:
                    return f"العربية"
                else:
                    return f"Search Finished"
            
            # Find url related title
            titles_str, url_content_map, url_items = await fetch_content(params.get('urls', []))

            # tell whether reading or just searching finished
            node.query = format_query(language=language)
            node.summary = thought_process + "\n" + reading_webpage_summary(language, url_items)
        
        elif DatastoreFinished().name == function:
            def format_query(language: str):
                if constants.CHINESE == language:
                    return f"数据检索结束"
                elif constants.JAPANESE == language:
                    return f"データ検索完了"
                elif constants.ARABIC == language:
                    return f"اكتمال استرجاع البيانات"
                else:
                    return f"Data Retrieval Completed"
            node.query = format_query(language=language)
            node.summary = thought_process

        # update node processing type as DONE
        node.processing_type = ProcessingType.DONE
        # update runtime_info
        runtime_info['url_content_map'].update(url_content_map)
        for value in node.search_results:
            # get url uuid
            if value.url in runtime_info['url_map']:
                value.id = runtime_info['url_map'][value.url].id
            else:
                value.id = len(runtime_info['url_map']) + 1 # start from 1
                runtime_info['url_map'][value.url] = value

    def format_stockprice_summary(
        self,
        result: dict) -> dict:
        chart_data = []
        symbol = result.get('symbol', '')
        for row in result.get('historical', []):
            chart_data.append({
                "date": row['date'],
                "open": round(float(row['open']), 2),
                "high": round(float(row['high']), 2),
                "low": round(float(row['low']), 2),
                "close": round(float(row['close']), 2),
                "volume": int(row['volume'])
            })
        # To avoid too big graph
        vega_spec = self.create_candlestick_chart(chart_data[:30], symbol)
        return f"""```vega
    {json.dumps(vega_spec, indent=2, ensure_ascii=False)}
    ```
"""
    
    def create_candlestick_chart(
        self,
        data: List[dict], 
        symbol: str,
        width: int = 800):
        """Create candlestick chart"""
        data_count = len(data)
        if data_count <= 10:
            padding = 0.3
        elif data_count <= 20:
            padding = 0.2
        else:
            padding = 0.1
        return {
            "$schema": "https://vega.github.io/schema/vega/v5.json",
            "width": width,
            "height": 400,
            "padding": {"left": 50, "top": 30, "right": 30, "bottom": 50},
            "title": {
                "text": f"{symbol}",
                "fontSize": 16,
                "anchor": "middle"
            },
            "data": [
                {
                    "name": "stock_data",
                    "values": data,
                    "transform": [
                        {
                            "type": "formula",
                            "expr": "datum.open <= datum.close",
                            "as": "up"
                        }
                    ]
                }
            ],
            "scales": [
                {
                    "name": "x",
                    "type": "band",
                    "range": "width",
                    "domain": {"data": "stock_data", "field": "date"},
                    "padding": padding
                },
                {
                    "name": "y",
                    "type": "linear",
                    "range": "height",
                    "nice": True,
                    "zero": False,
                    "domain": {
                        "data": "stock_data",
                        "fields": ["low", "high"]
                    }
                },
                {
                    "name": "color",
                    "type": "ordinal",
                    "range": ["#ae1325", "#06982d"],
                    "domain": [False, True]
                }
            ],
            "axes": [
                {
                    "orient": "bottom",
                    "scale": "x",
                    "title": "Date",
                    "labelAngle": -45,
                    "labelAlign": "right"
                },
                {
                    "orient": "left",
                    "scale": "y",
                    "title": "Price ($)"
                }
            ],
            "marks": [
                {
                    "type": "rule",
                    "from": {"data": "stock_data"},
                    "encode": {
                        "enter": {
                            "x": {"scale": "x", "field": "date", "band": 0.5},
                            "y": {"scale": "y", "field": "low"},
                            "y2": {"scale": "y", "field": "high"},
                            "stroke": {"scale": "color", "field": "up"},
                            "strokeWidth": {"value": 1}
                        }
                    }
                },
                {
                    "type": "rect",
                    "from": {"data": "stock_data"},
                    "encode": {
                        "enter": {
                            "x": {"scale": "x", "field": "date"},
                            "width": {"scale": "x", "band": 1},
                            "y": {"scale": "y", "signal": "min(datum.open, datum.close)"},
                            "y2": {"scale": "y", "signal": "max(datum.open, datum.close)"},
                            "fill": {"scale": "color", "field": "up"},
                            "stroke": {"scale": "color", "field": "up"},
                            "strokeWidth": {"value": 1}
                        },
                        "update": {
                            "fillOpacity": {"value": 0.8}
                        },
                        "hover": {
                            "fillOpacity": {"value": 1},
                            "tooltip": {
                                "signal": "{'Date': datum.date, 'Open': datum.open, 'High': datum.high, 'Low': datum.low, 'Close': datum.close}"
                            }
                        }
                    }
                }
            ]
        }

    def _format_pubmed_weblink(
        self,
        pubmed_response: dict) -> WebSearchLink:
        # init web search link
        # set url
        pubmed_response['url'] = f"https://pubmed.ncbi.nlm.nih.gov/{pubmed_response.get('uid', '')}"
        pubmed_response['pubmed_id'] = pubmed_response.get('uid', '')
        
        # get article ids, i.e. pmc, doi
        article_ids = pubmed_response.get('articleids', [])
        article_ids = {item.get('idtype', ''): item.get('value', '') for item in article_ids if isinstance(item, dict)}
        # get author
        authors = ",".join([item.get('name', '') for item in pubmed_response.get('authors', [])])

        link = WebSearchLink(
            pubmed_id=pubmed_response.get("uid", ''),
            pmcid=article_ids.get('pmcid', ''),
            pmc=article_ids.get('pmc', ''),
            pii=article_ids.get('pii', ''),
            doi=article_ids.get("DOI", ""),
            summ=pubmed_response.get('summary', '')[:100],
            url=pubmed_response.get('url', ''),
            title=pubmed_response.get("title", ""),
            site_name=pubmed_response.get('source', "PubMed"),
            issn=pubmed_response.get("issn", ""),
            essn=pubmed_response.get("essn", ""),
            full_journal_name=pubmed_response.get("fulljournalname", ""),
            nlm_id=pubmed_response.get("nlmuniqueid", ""),
            pub_date=pubmed_response.get("pubdate", ''),
            author=authors,
            type=SearchType.PUBMED,
        )

        # get sci if
        key_word = link.issn or link.full_journal_name or link.nlm_id or link.site_name
        if key_word != '':
            sciif_response = self.sci_if_client.search_by_issn(value=key_word)
            if 'factor' in sciif_response:
                link.cite_score = str(sciif_response['factor'])
                pubmed_response['cite_score'] = str(sciif_response['factor'])

        return link

    def _emint_link(
        self,
        item: dict,
        is_pubmed: bool = False) -> dict:

        res = {
            "id": item.get('id', ''),
            'title': item.get('title', ''),
            'summary': item.get('summary', '') or item.get('summ', ''),
        }

        if is_pubmed:
            res.update({
                'fulljournalname': item.get('fulljournalname', '') or item.get('full_journal_name', ''),
                'epubdate': item.get('epubdate', ''),
                'source': item.get('source', ''),
                'authors': item.get('authors', ''),
                'sci_if': item.get('sci_if', ''),
            })
        else:
            res.update({
                'site_name': item.get('site_name', ''),
                'summary': item.get('summ', ''),
                'content': item.get('content', '')[:15*1000], #TODO use this 
            })

        return res
    
    def _format_history_action(
        self,
        runtime_info: dict) -> str:

        history_search = copy.deepcopy(runtime_info['history_search'])

        tools = ""
        stock_history_prices = []
        financial_statements = []
        company_info = {}
        for index, search in enumerate(history_search, start=1):
            function = search.get('function', '')
            params = search.get('params', {})
            thought_process = params.get('thought_process', '')
            tool = {'tool_name': function, 'thought_process': thought_process}
            
            if function in [MedicalSearch().name, GeneralSearch().name, NewsSearch().name, PatentSearch().name]:
                keywords = []
                for sub_query in search.get('sub_queries', []):
                    keywords.append({
                        "keyword": sub_query.get('key_word', ''),
                        "prefer_region": sub_query.get('region', ''),
                    })
                tool['query'] = keywords
            elif function == PubmedArticlesSearch().name:
                tool['query'] = params.get('pubmed_query', '')
            elif function in [StockHistoricalPriceQuery().name, StockNewsSearch().name]:
                tool['query'] = f"Symbol: {params.get('symbol', '')}, From: {params.get('date_from', '')}, To: {params.get('date_to', '')}"
                if function == StockHistoricalPriceQuery().name:
                    stock_history_prices.append(search.get('result', {}))
            elif function == CompanyInfoQuery().name:
                tool['query'] = f"Symbol: {params.get('symbol', '')}"
                company_info = search.get('result', {})
            elif function in [FinancialStatements().name, ChinaCompanyFinancialStatements().name] :
                tool['query'] = f"Symbol: {params.get('symbol', '')}, Period: {params.get('period', '')}"
                financial_statements.append(search.get('result', {}))
            else:
                tool['query'] = params.pop('thought_process', None)
            
            tools += f"""<step_{index}>
            {tool}
            </step_{index}>\n"""
        
        source = sorted(list(runtime_info['url_map'].values()), key=lambda x: x.id)
        websearch_results = self._format_source(runtime_info, source)

        tools += f"""<search_results>
        {json.dumps(websearch_results, separators=(',', ':'), ensure_ascii=False)}
        </search_results>"""

        if len(stock_history_prices) > 0 :
            tools += f"""<history_stock_price>{json.dumps(stock_history_prices, separators=(',', ':'), ensure_ascii=False)}</history_stock_price>"""

        if company_info:
            tools += f"""<company_info>{json.dumps(company_info, separators=(',', ':'), ensure_ascii=False)}</company_info>"""

        if len(financial_statements) > 0:
            tools += f"""<financial_statements>{json.dumps(financial_statements, separators=(',', ':'), ensure_ascii=False)}</financial_statements>"""

        return tools
    
    def _format_source(
        self,
        runtime_info: dict,
        source: List[WebSearchLink]):
        
        websearch_results = f""
        for link in source:
            link_json = link.model_dump()
            if link.type == SearchType.PUBMED:
                if link.url in runtime_info['url_content_map']:
                    link_json['summ'] = runtime_info['url_content_map'][link.url]
                link_json =  self._emint_link(link_json, True)
            else:
                if link.url in runtime_info['url_content_map']:
                    link_json['content'] = runtime_info['url_content_map'][link.url]
                link_json = self._emint_link(link_json)
            
            id = link_json.pop('id', None)
            webpage_content = f"[citation:{id}]\n"
            for key, value in link_json.items():
                if value != '':
                    webpage_content += f"{key}: {value}\n"
            webpage_content += f"[citation:{id}]\n"
            websearch_results += webpage_content
        
        return websearch_results
    
    def _format_final_searchresults(
        self,
        runtime_info: dict,
        history_messages: List[dict] = []) -> tuple[str, List[dict]]:

        # format history search, add thought_process to final output llm
        history_search = "\n".join([
            f"{search.get('function', '')} : {search.get('params', {}).get('thought_process', '')}" 
            for search in runtime_info['history_search']
        ])

        # format source
        runtime_info['source'] = [
            self._format_final_source(item.id, item)
            for item in runtime_info['url_map'].values()
        ]

        # sort web search result
        # 1. links with content
        # 2. PubMed search results
        # 3. Patents
        # 4. Common links
        source = []
        url_set = set()

        # Fetch links with content
        for url, item in runtime_info['url_map'].items():
            if url in runtime_info['url_content_map']:
                source.append(item)
                url_set.add(url)
        
        # Fetch PubMed links
        for url, item in runtime_info['url_map'].items():
            if url not in url_set and item.type == SearchType.PUBMED:
                source.append(item)
                url_set.add(url)

        # Fetch Patent links
        for url, item in runtime_info['url_map'].items():
            if url not in url_set and item.type == SearchType.PATENT:
                source.append(item)
                url_set.add(url)
        
        # Add left links
        for url, item in runtime_info['url_map'].items():
            if url not in url_set:
                source.append(item)

        # format websearch result str
        websearch_results = self._format_source(runtime_info, source)

        # add stock prices
        stock_prices = [
            search.get('result', {})
            for search in runtime_info['history_search'] if search.get('function', '') == StockHistoricalPriceQuery().name
        ]

        # add financial statements
        financial_statements = [
            search.get('result', {})
            for search in runtime_info['history_search'] if search.get('function', '') in [FinancialStatements().name, ChinaCompanyFinancialStatements().name]
        ]

        # truncate input prompt
        #logger.info(f"{websearch_results}")
        """
        websearch_results, history_messages = tokenizer.truncate_messages_by_tokens(
            json.dumps(websearch_results, separators=(',', ':'), ensure_ascii=False),
            history_messages,
            56*1000,
            "deepseek"
        )
        """
        #logger.info(f"{websearch_results}")

        final_result = f"""<history_search>
            {json.dumps(history_search, separators=(',', ':'), ensure_ascii=False)}
            </history_search>
            <websearch_resutls>
            {json.dumps(websearch_results, separators=(',', ':'), ensure_ascii=False)}
            </websearch_resutls>
            """
        if len(stock_prices) > 0:
            final_result += f"""<stock_prices>{json.dumps(stock_prices, separators=(',', ':'), ensure_ascii=False)}</stock_prices>"""
        
        if len(financial_statements) > 0:
            final_result += f"""<financial_statements>{json.dumps(financial_statements, separators=(',', ':'), ensure_ascii=False)}</financial_statements>"""

        # TODO GPT4.1 cannot handle reference very well, remove reference in history messages
        def remove_reference(history_messages: list):
            for message in history_messages:
                if 'role' in message and message['role'] == 'assistant':
                    message['content'] = re.sub(r'\[\d+\]\([^)]+\)', '', message['content'])
            return history_messages

        return final_result, remove_reference(history_messages)

    async def use_tool(self, user_prompt: str, history_messages: List[dict] = [], images: List[str] = [], **kwargs):
        start_time = time.time()

        # init components
        runtime_info = {
            'history_search': [],
            'url_map': {},
            'url_content_map': {},
        }
        language, background, model, enable_rag = self._init_components(kwargs=kwargs)
        # immediately return an empty node for frontend to show user's question
        response = self.helper.init_response(self)
        yield response

        # TODO remove
        is_hitl = kwargs.get('is_hitl', False)

        # query rewrite
        if enable_rag:
            
            # add thinking node tell user current working
            self._add_thinking_node(response, user_prompt, language)
            yield response

            async for tmp_response in self._task_with_heartbeat(
                response, 
                self._query_rewrite,
                response,
                runtime_info,
                user_prompt,
                history_messages,
                background,
                language):
                yield tmp_response

            websearch_results, history_messages = self._format_final_searchresults(runtime_info, history_messages)

            # add the related search summary in finally user prompt, performance is batter than add them in the history messages.
            if constants.DEEPSEEK_R1 == model:
                final_user_prompt = rag_r1_final_output_pt.format(
                    background=background,
                    websearch_results=websearch_results,
                    user_prompt=user_prompt,
                    current_datetime=datetime.now().strftime('%Y-%m-%d.'),
                    language=language)
            else:
                
                final_user_prompt = ds_search_final_output_user_pt.format(
                    current_date=datetime.now().strftime('%Y-%m-%d.'),
                    language=language,
                    background=background,
                    websearch_results=websearch_results,
                    user_question=user_prompt)
                
        else:

            pt_template = r1_refer_norag_pt if constants.DEEPSEEK_R1 == model else norag_final_output_pt
            final_user_prompt = pt_template.format(
                background=background,
                user_prompt=user_prompt,
                current_datetime=datetime.now().strftime('%Y-%m-%d.'),
                language=language)

        logger.info(f"Mindesearch final response input: {len(history_messages)} {final_user_prompt[:300]}...{final_user_prompt[-300:]}")

        tmp_response = MindSearchResponse(processing_type=ProcessingType.RESPONSING)
        
        # 检查 final_output_agent 类型，决定是否使用增强的最终输出
        if isinstance(self.final_output_agent, MindSearchWithCoderFinalOutputAgent):
            # 使用增强的最终输出，支持意图识别和代码计算
            logger.info("检测到 MindSearchWithCoderFinalOutputAgent，使用增强的最终输出")
            
            # 过滤掉 coder_agent 等非 LLM 支持的参数
            llm_allowed_keys = {"user_prompt", "history_messages", "search_results", "images"}
            filtered_kwargs = {k: v for k, v in kwargs.items() if k in llm_allowed_keys}
            
            async for chunk in self.final_output_agent.use_tool(
                user_prompt=final_user_prompt, 
                history_messages=history_messages,
                search_results=websearch_results,  # 传递搜索结果用于意图识别
                **filtered_kwargs
            ):
                if not chunk:
                    continue
                if is_hitl:
                    tmp_response.content = chunk
                    yield tmp_response
                else:
                    response.content = chunk
                    yield response
        else:
            # 使用原有的 _final_output 方法
            logger.info("使用原有的最终输出方式")
            async for chunk in self._final_output(user_prompt=final_user_prompt, history_messages=history_messages):
                if not chunk:
                    continue
                if is_hitl:
                    tmp_response.content = chunk
                    yield tmp_response
                else:
                    response.content = chunk
                    yield response

        if is_hitl:
            response.content = tmp_response.content
        self._format_final_output(response=response, model=model, language=language, runtime_info=runtime_info)
        yield response

        response.processing_type = ProcessingType.RESPONSEDONE
        yield response
        
        logger.info(f"MindSearch final output: {response.content} {response} cost {time.time() - start_time}s")
        if not kwargs.get('skip_followup', False):
            # Add follow update questions.
            response = await self._followup_questions(user_prompt=user_prompt, response=response, history_messages=history_messages, language=language)
            yield response


class MindSearchWebHitlAgent(MindSearchAgentV2):

    query_rewrite_agent: MindSearchWebHitlQueryRewriteAgent = MindSearchWebHitlQueryRewriteAgent()

    def _format_qr_prompt(
        self,
        history_search: dict,
        query: str,
        background: str = '',
        language: str = constants.ENGLISH) -> str:
        
        user_pt = gpt_query_rewrite_user_pt.format(
            current_date=datetime.now().strftime('%Y-%m-%d'),
            language=language,
            history_search=history_search,
            background=background,
            user_question=query,
        )
        return gpt_web_qr_sys_pt + user_pt


class MindSearchMedicalHitlAgent(MindSearchAgentV2):

    query_rewrite_agent: MindSearchMedicalHitlQueryRewriteAgent = MindSearchMedicalHitlQueryRewriteAgent()

    def _format_qr_prompt(
        self,
        history_search: dict,
        query: str,
        background: str = '',
        language: str = constants.ENGLISH) -> str:
        
        user_pt = gpt_query_rewrite_user_pt.format(
            current_date=datetime.now().strftime('%Y-%m-%d'),
            language=language,
            history_search=history_search,
            background=background,
            user_question=query,
        )
        return gpt_medical_qr_sys_pt + user_pt


class MindSearchPatentHitlAgent(MindSearchAgentV2):

    query_rewrite_agent: MindSearchPatentHitlQueryRewriteAgent = MindSearchPatentHitlQueryRewriteAgent()

    def _format_qr_prompt(
        self,
        history_search: dict,
        query: str,
        background: str = '',
        language: str = constants.ENGLISH) -> str:
        
        user_pt = gpt_query_rewrite_user_pt.format(
            current_date=datetime.now().strftime('%Y-%m-%d'),
            language=language,
            history_search=history_search,
            background=background,
            user_question=query,
        )
        return gpt_patent_qr_sys_pt + user_pt
    
class MindSearchNewsHitlAgent(MindSearchAgentV2):

    query_rewrite_agent: MindSearchNewsHitlQueryRewriteAgent = MindSearchNewsHitlQueryRewriteAgent()

    def _format_qr_prompt(
        self,
        history_search: dict,
        query: str,
        background: str = '',
        language: str = constants.ENGLISH) -> str:
        
        user_pt = gpt_query_rewrite_user_pt.format(
            current_date=datetime.now().strftime('%Y-%m-%d'),
            language=language,
            history_search=history_search,
            background=background,
            user_question=query,
        )
        return gpt_news_qr_sys_pt + user_pt

class MindSearchFinanceHitlAgent(MindSearchAgentV2):

    query_rewrite_agent: FinanicalModleingPrepAgent = FinanicalModleingPrepAgent()
    final_output_agent: MindSearchWithCoderFinalOutputAgent = MindSearchWithCoderFinalOutputAgent()

    def _format_qr_prompt(
        self,
        history_search: dict,
        query: str,
        background: str = '',
        language: str = constants.ENGLISH) -> str:
        
        user_pt = gpt_query_rewrite_user_pt.format(
            current_date=datetime.now().strftime('%Y-%m-%d'),
            language=language,
            history_search=history_search,
            background=background,
            user_question=query,
        )
        return gpt_finance_qr_sys_pt + user_pt

