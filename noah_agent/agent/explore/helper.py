import re
import tiktoken
from typing import Any, Callable
from functools import wraps
from urllib.parse import urlparse

import agent.explore.constants as constants
from agent.explore.schema import (MindSearchResponse, ProcessingType, SearchNode, SearchType)



class MindSearchHelper:

    def init_response(self, agent_name: str) -> MindSearchResponse:
        return MindSearchResponse()
    
    def search_processing_stages(self, agent_name: str, response: MindSearchResponse) -> MindSearchResponse:
        processing_stages = ['Analyzing', 'Searching', 'Answering', 'Finished']

        if 'cn' in agent_name:
            processing_stages = ['分析问题', '网络搜索', '整理答案', '完成']
        elif 'jp' in agent_name:
            processing_stages = ['問題を分析する', 'ネット検索', '答えを整理する', '完了']
        elif 'arsa' in agent_name:
            processing_stages = ['تحليل المشكلة', 'البحث عبر الإنترنت', 'تنظيم الإجابة', 'إكمال']

        response.processing_stages.stages = processing_stages
        response.processing_stages.stage_index += 1

        return response
    
    def get_intention_language(self, language: str) -> str:
        language = language.lower()

        if constants.CN == language:
            return constants.CHINESE
        elif constants.JP == language:
            return constants.JAPANESE
        elif constants.ARSA == language:
            return constants.ARABIC
        else:
            return constants.ENGLISH
        
    def get_context(self, kwargs: dict) -> tuple[str, str, str, bool]:
        r"""Get context"""

        params = kwargs.get('params', {})
        
        language = self.get_intention_language(params.get('language', ''))
        model = params.get('model', '')
        background = params.get('background', '')
        enable_rag = params.get('enable_rag', True)
        
        return language, background, model, enable_rag 

    
    def websearch_fail_reason(self, node: SearchNode, language: str) -> str:
        if node.search_type == SearchType.PUBMED:
            if node.key_word != '':
                if constants.CHINESE == language:
                    return f'**PubMed Search Term:** {node.key_word} \n 无法获取到足够的搜索结果，请切换成PubMed搜索尝试使用专业论文检索。'
                elif constants.JAPANESE == language:
                    return f'**PubMed Search Term:** {node.key_word} \n 十分な検索結果が得られませんでした。専門的な論文を検索するには、PubMed検索に切り替えてください。'
                elif constants.ARABIC == language:
                    return f'**PubMed Search Term:** {node.key_word} \n لم يتم العثور على نتائج بحث كافية. يُرجى التبديل إلى بحث PubMed لمحاولة الوصول إلى أوراق بحثية متخصصة.'
                else:
                    return f'**PubMed Search Term:** {node.key_word} \n Not enough search results were found. Please switch to PubMed search to try retrieving professional academic papers.'

        if language == 'Chinese':
            return "搜索因网络问题失败。您可以浏览以下链接获取更多信息或帮助。"
        elif language == 'Japanese':
            return "ネットワークの問題により検索が失敗しました。以下のリンクを参照して、詳しい情報やサポートをご利用ください。"
        elif language == 'Arabic':
            return "فشل البحث بسبب مشكلة في الشبكة. يمكنك استكشاف الروابط التالية لمزيد من المعلومات أو المساعدة."
        else:
            return "Search failed due to a network issue. You can explore the following links for more information or assistance."        
        
    def websearch_no_results(self, node: SearchNode, language: str) -> str:
        if language == 'Chinese':
            return "未能搜索到相关信息，您可以重试或者修改问题。"
        elif language == 'Japanese':
            return "関連する情報が見つかりませんでした。再試行するか、質問を変更してください。"
        elif language == 'Arabic':
            return "لم يتم العثور على معلومات ذات صلة. يمكنك المحاولة مرة أخرى أو تعديل سؤالك."
        else:
            return "No relevant information was found. You can try again or modify your question."    

    def pubmed_search_query(self, language: str) -> str:
        if language == 'Chinese':
            return "PubMed学术文献搜索"
        elif language == 'Japanese':
            return "PubMed学術文献検索"
        elif language == 'Arabic':
            return "البحث عن الأدبيات الأكاديمية"
        else:
            return "PubMed Search"
    
    def get_finish_stages(self, response: MindSearchResponse) -> MindSearchResponse:
        stage = len(response.processing_stages.stages) - 1
        response.processing_stages.stage_index = stage
        return response

    def dict_to_xml(self, data, root_name: str):
        if isinstance(data, dict):
            xml_str = f"<{root_name}>"
            for key, value in data.items():
                xml_str += self.dict_to_xml(value, key)
                xml_str += f"</{root_name}>"
                return xml_str
        elif isinstance(data, list):
            xml_str = ""
            for item in data:
                xml_str += self.dict_to_xml(item, root_name)
                return xml_str
        else:
            return f"<{root_name}>{str(data)}</{root_name}>"
        
    def _count_tokens(self, text: str, model: str) -> int:
        encoding = tiktoken.encoding_for_model(model)
        return len(encoding.encode(text))

    def truncate_messages(self, user_prompt: str, history_messages: list[dict] = [], max_tokens: int = 128000, model: str = 'gpt-4') -> list:
        total_tokens = 0
        truncated_messages = []

        messages = history_messages + [{"role": "user", "content": user_prompt}]
        
        for message in reversed(messages):
            message_tokens = self._count_tokens(message.get('content', ''), model)
            if total_tokens + message_tokens <= max_tokens:
                truncated_messages.insert(0, message)
                total_tokens += message_tokens
            else:
                break
        
        truncated_messages.pop()
        return truncated_messages

    def format_reference(self, output: str, url_map: dict = {}, default_site_name: str = '') -> tuple[str, list]:
        pattern = r'\[(.*?)\]\(([\w+.-]+:[^\s\)]+)\)'

        def format_reference(output: str, url_map: dict) -> str:
            def replace_func(match):
                url = match.group(2)
                if url not in url_map:
                    site_name = self.get_site_name(url, default=default_site_name)
                    url_map[url] = {
                        'id': len(url_map) + 1,
                        'url': url,
                        'site_name': site_name,
                        'title': site_name,
                        'summary': '',
                    }
                num = url_map[url]['id']
                return f'[{num}]({url})'
            
            output = re.sub(pattern, replace_func, output)
            return output
        
        output = format_reference(output=output, url_map=url_map)

        return output, list(url_map.values())
    
    def get_site_name(self, url: str, default: str) -> str:
        # Try to extract domain from URL using regex
        pattern = r'(?:https?:\/\/)?(?:www\.)?([^\/\s]+)'
        match = re.search(pattern, url)
        if match:
            domain = match.group(1)
            # Remove TLD and split by dots
            site_name = domain.split('.')[0]
            # Convert to title case for better readability
            return site_name.title()
    
        return default
    
    def format_output_reference(self,
                                output: str,
                                reference_patterns: list[str] = [r'\[(.*?)\]\(([\w+.-]+:[^\s\)]+)\)']) -> str:
        def replace_func(match):
            num = match.group(1)
            url = match.group(2)
            return f'[{num}]({url})'
        
        for patterns in reference_patterns:
            output = re.sub(patterns, replace_func, output)
        return output
    
    def format_invalid_reference(self,
                                 output: str,
                                 node: SearchNode,
                                 reference_patterns: list[str] = [r'\[(.*?)\]\(([\w+.-]+:[^\s\)]+)\)']) -> str:
        
        def replace_func(match):
            num = match.group(1)
            if not num.isdigit():
                return ''
            num = int(num)
            if num - 1 >= 0 and num - 1 < len(node.search_results):
                url = node.search_results[num - 1].url
                return f'[{num}]({url})'
            else:
                return ''
        
        for patterns in reference_patterns:
            output = re.sub(patterns, replace_func, output)
        return output
    
    def get_domain(self, url: str) -> str:
        parsed_url = urlparse(url)
        domain_with_port = parsed_url.netloc
        domain = domain_with_port.split(':')[0]
        return domain
    
    def format_invalid_citation(self,
                                output: str,
                                reference_patterns: list[str] = [r'【citation:(\s+)】']) -> str:
        for patterns in reference_patterns:
            output = re.sub(patterns, r'[citation:\1]', output)

        output = re.sub(r'【citation:[^】]*】', '', output)
        return output
    
    def format_citation(self,
                         url_list: list[dict],
                         content: str,
                         reference_patterns: list[str] = [r'\[citation:([0-9,\s，]+)\]']) -> str:
        # 支持多个数字，分隔符为英文逗号、中文逗号、空格
        def replace_func(match):
            nums_str = match.group(1)
            # 用英文逗号、中文逗号、空格分割
            nums = re.split(r'[，,\s]+', nums_str)
            result = ''
            for num in nums:
                if not num.isdigit():
                    continue
                idx = int(num)
                if idx - 1 < 0 or idx - 1 >= len(url_list):
                    continue
                url = url_list[idx - 1]['url']
                url = url.replace("(", "%28").replace(")", "%29")
                result += f'[{idx}]({url})'
            return result
        
        for pattern in reference_patterns:
            content = re.sub(pattern, replace_func, content)
        
        return content
    
    def remove_empty_mermaid_blocks(self, markdown_text: str) -> str:
        pattern = re.compile(r'```mermaid\s*\n(.*?)\n?```', re.DOTALL)

        def replacer(match):
            content = match.group(1)
            if content.strip() == "":
                return ""
            else:
                return match.group(0)

        cleaned = pattern.sub(replacer, markdown_text)
        return cleaned
    
    def omit_markdown_block(self, markdown_text: str, pattern: str = r"```mermaid\s*(.*?)```") -> str:
        blocks = re.findall(pattern, markdown_text, re.S)
        if not blocks:
            return ""
        return f"```mermaid\n{blocks[-1]}```"
