import time
import logging

import agent.explore.constants as constants

from datetime import datetime
from typing import List
from openai.types.chat import ChatCompletionMessage

from agent.core.preset import AgentPreset
from agent.core.preset import AgentPreset
from agent.explore.helper import MindSearchHelper
from agent.explore.mindsearch_agent_v2 import MindSearchAgentV2
from agent.explore.schema import (MindSearchResponse, SearchType, SearchNode, ProcessingType)
from agent.explore.mindsearch_rewrite_prompt_v2 import (gpt_confirmation_sys_pt, gpt_search_sys_pt, gpt_rewrite_sys_pt,
                                                        gpt_help_user_pt, gpt_rewrite_user_pt)
from llm.azure_models import GPT41
from llm.base_model import BaseLLM
from llm.composite_models import Compositeo3, Compositeo4mini
from tools.core.base_tool import BaseTool
from tools.explore.mindsearch_tools_v2 import GeneralSearch
from tools.explore.rewrite_tools import Confirming, Rewriting

logger = logging.getLogger(__name__)


class MindSearchConfirmationAgent(AgentPreset):
    llm: BaseLLM = Compositeo4mini
    sys_prompt: str = ''
    tools: List[BaseTool] = [
        Confirming,
    ]
    tool_choice: str = "required"


class MindSearchWebsearchAgent(AgentPreset):
    llm: BaseLLM = Compositeo4mini
    sys_prompt: str = ''
    tools: List[BaseTool] = [
        GeneralSearch,
    ]
    tool_choice: str = "required"


class MindSearchRewriteAgent(AgentPreset):
    llm: BaseLLM = Compositeo4mini
    sys_prompt: str = ''
    tools: List[BaseTool] = [
        Rewriting,
    ]
    tool_choice: str = "required"


class MindSearchRewriteAgentV2(MindSearchAgentV2):
    llm: BaseLLM = Compositeo3

    helper: MindSearchHelper = MindSearchHelper()
    confirmation_agent: MindSearchConfirmationAgent = MindSearchConfirmationAgent()
    websearch_agent: MindSearchWebsearchAgent = MindSearchWebsearchAgent()
    rewrite_agent: MindSearchRewriteAgent = MindSearchRewriteAgent()

    def _init_components(self, kwargs) -> tuple[str, str]:
        r"""
        Init agent components by language or whether need rag.
        """

        language, _, _, _ = self.helper.get_context(kwargs=kwargs)
        params = kwargs.get('params', {})
        feedbacks = params.get('feedbacks', [])
        rewrites = params.get('rewrites', [])
        tool_use_context = params.get('tool_use_context', '')

        return language, feedbacks, rewrites, tool_use_context
    
    async def _query_rewrite(
        self,
        query: str,
        rewrites: list,
        feedbacks: list,
        tool_use_context: str,
        response: MindSearchResponse,
        language: str
    ):
        r"""
        1. Check whether need confirmation.
        2. Do web search for translation or fact checking.
        3. Rewriting
        """

        nfb = ""
        for confirmation, feedback in zip(rewrites, feedbacks):
            nfb += f"Item to Confirm: {confirmation} \n User Answer: {feedback} \n"
    
        chunk = None
        need_confirmation = True
        if len(feedbacks) <= 2:
            node = self._add_thinking_node(response, Confirming.__name__, language)
            yield response

            user_prompt = gpt_confirmation_sys_pt + gpt_help_user_pt.format(
                current_date=datetime.now().strftime('%Y-%m-%d'),
                language=language,
                feedbacks=nfb,
                user_question=query,
                context=tool_use_context
            )
            
            async for chunk in self.confirmation_agent.use_tool(user_prompt=user_prompt):
                if isinstance(chunk, ChatCompletionMessage):
                    logger.info(f"[_query_rewrite_with_mindsearch] gpt output {chunk}")

                elif isinstance(chunk, dict):    
                    # process fc result
                    await self._process_fc_result(chunk, node, response, language)
                    need_confirmation = chunk.get('params', {}).get('need_confirmation', False)
                    if need_confirmation:
                        node.processing_type = ProcessingType.DONE
                    yield response

        if len(feedbacks) >= 2 or not need_confirmation:
            node = self._add_thinking_node(response, GeneralSearch.__name__, language)
            yield response

            user_prompt = gpt_search_sys_pt + gpt_help_user_pt.format(
                current_date=datetime.now().strftime('%Y-%m-%d'),
                language=language,
                feedbacks=nfb,
                user_question=query,
                context=tool_use_context
            )

            async for chunk in self.websearch_agent.use_tool(user_prompt=user_prompt):
                if isinstance(chunk, ChatCompletionMessage):
                    logger.info(f"[_query_rewrite_with_mindsearch] gpt output {chunk}")

                elif isinstance(chunk, dict):    
                    # process fc result
                    await self._process_fc_result(chunk, node, response, language)
                    yield response

            # rewrite
            url_map = {}
            for sub_query in chunk.get('sub_queries', []):
                for value in sub_query.get('search_result', {}).values():
                    url_map[value.get('url')] = value

            search = "\n".join([
                f"Title: {value.get('title', '')}\n, Snippet: {value.get('summ', '')}\n SiteName: {value.get('site_name', '')}"
                for value in url_map.values()
            ])

            node = self._add_thinking_node(response, Rewriting.__name__, language)
            yield response
            user_prompt = gpt_rewrite_sys_pt + gpt_rewrite_user_pt.format(
                current_date=datetime.now().strftime('%Y-%m-%d'),
                language=language,
                websearch_results=search,
                feedbacks=nfb,
                user_question=query,
                context=tool_use_context
            )

            async for chunk in self.rewrite_agent.use_tool(user_prompt=user_prompt):
                if isinstance(chunk, ChatCompletionMessage):
                    logger.info(f"[_query_rewrite_with_mindsearch] gpt output {chunk}")

                elif isinstance(chunk, dict):    
                    # process fc result
                    await self._process_fc_result(chunk, node, response, language)
                    node.processing_type = ProcessingType.DONE
                    yield response

    def _add_thinking_node(
        self,
        response: MindSearchResponse,
        tool_name: str,
        language: str = constants.ENGLISH) -> SearchNode:

        if not response.search_graph:
            response.search_graph = SearchNode(
                search_type=SearchType.UNKNOWN,
                thought_process='MindSearch Rewrite')
            response.processing_type = ProcessingType.PROCESSING

        elif len(response.search_graph.children) > 0:
            node = response.search_graph.children[-1]
            if node.processing_type == ProcessingType.THINKING:
                return node
        
        node = SearchNode(
            search_type=SearchType.UNKNOWN,
            query=self._tool_name_translation(tool_name, language),
            processing_type=ProcessingType.THINKING)
        response.search_graph.add_child(node)

        return node

    def _tool_name_translation(
        self,
        tool_name: str,
        language: str):
        if tool_name == GeneralSearch.__name__:
            if constants.CHINESE == language:
                return "网络搜索"
            elif constants.JAPANESE == language:
                return "ウェブ検索"
            elif constants.ARABIC == language:
                return "بحث على الإنترنت"
            else:
                return "Web Search"
        elif tool_name == Confirming.__name__:
            if constants.CHINESE == language:
                return "问题澄清"
            elif constants.JAPANESE == language:
                return "問題の明確化"
            elif constants.ARABIC == language:
                return "توضيح المشكلة"
            else:
                return "Problem Clarification"
        else:
            if constants.CHINESE == language:
                return "改写问题"
            elif constants.JAPANESE == language:
                return "改訂結果"
            elif constants.ARABIC == language:
                return "النتيجة المعاد كتابتها"
            else:
                return "Rewritten Question"

    async def _process_fc_result(
        self,
        result: dict,
        node: SearchNode,
        response: MindSearchResponse,
        language: str,
    ):
        function = result.get('function', '')
        params = result.get('params', {})
        thought_process = params.get('thought_process', '')

        if function == GeneralSearch.__name__:
            def keyword(language: str):
                if constants.CHINESE == language:
                    return "### 搜索词:"
                elif constants.JAPANESE == language:
                    return "### 検索語:"
                elif constants.ARABIC == language:
                    return "### كلمة البحث"
                else:
                    return "### Search Keywords:"

            # Merge query
            query = "\n".join([
                sub_query.get('key_word', '') if language != constants.ENGLISH else sub_query.get('key_word_en', '')
                for sub_query in result.get('sub_queries', [])
            ])
            # Merge summary 
            summary = keyword(language) + "\n" + "\n".join([
                f"- {sub_query.get('sub_query', '')}"
                for sub_query in result.get('sub_queries', [])
            ])

            node.query = self._tool_name_translation(function, language)
            node.search_type = SearchType.WEB
            node.summary = f"{thought_process}\n{summary}"
        else:
            node.query = self._tool_name_translation(function, language)
            node.summary = f"{thought_process}"
            response.content = params.get('confirmation', '') or params.get('rewrite', '')

        if function == Rewriting.__name__:
            response.processing_type = ProcessingType.REWRITE  
        
    async def use_tool(self, user_prompt: str, history_messages: List[dict] = [], images: List[str] = [], **kwargs):
        start_time = time.time()

        # init components
        language, feedbacks, rewrites, tool_use_context = self._init_components(kwargs=kwargs)
        response = self.helper.init_response(self)
        logger.info(f"[MindSearchRewrite] {language} {feedbacks} {rewrites} {user_prompt} {kwargs}")

        async for tmp_response in self._query_rewrite(user_prompt, rewrites, feedbacks, tool_use_context, response, language):
            yield tmp_response

        logger.info(f"MindSearch Rewrite final output {response.content}")