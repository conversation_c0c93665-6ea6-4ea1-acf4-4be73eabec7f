from datetime import datetime
import io
import re
import time
import traceback
from typing import Callable, List, Type
from agent.human_in_loop.query import run_query
from agent.human_in_loop.utils import *
from agent.human_in_loop.constants import background_prefix_map, data_empty_prompt, data_empty_prompt_cn, tool_mapping
from agent.explore.mindsearch_workflow_agent import MindSearchWorkflowAgent
from agent.explore.mindsearch_agent import MindSearchAgent, MindSearchOfficialAgent
from agent.explore.mindsearch_rewrite_agent import MindSearchRewriteAgent
from agent.explore.schema import ProcessingType
from llm.ali_models import CompositeQwen3, Qwen3
from llm.composite_models import SlotFillingModels
from tools.human_in_loop.planning.schema import *
from tools.human_in_loop.planning.schema_cn import *
from tools.human_in_loop.planning.prompt import *
from utils.human_in_loop.helpers import *
from utils.core.get_json_schema import get_openai_json_schema_v3
from agent.core.preset import AgentPreset
from llm.base_model import BaseLLM
from llm.deepseek_models import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>kChat, CompositeDeepseekChat, CompositeDeepseekReasoner
from utils.clinical_utils.clean_args_typesense import clean_args_typesense
import json
import csv
import logging

logger = logging.getLogger(__name__)

MAX_STEPS = 5
MAX_STEPS_WITH_REFLECTION = 9

class PlanningAgent(AgentPreset):
    llm: BaseLLM = CompositeDeepseekChat(max_retries=0, timeout=15, first_chunk_timeout=10)
    plan_llm: BaseLLM = ClaudeThenDeepseekChat(max_retries=0, timeout=15, first_chunk_timeout=10)
    # plan_llm: BaseLLM = CompositeDeepseekChat(max_retries=0, timeout=15, first_chunk_timeout=10)
    plan_extraction_llm: BaseLLM = SlotFillingModels(max_retries=0, timeout=15, first_chunk_timeout=10)
    slot_filling_llm: BaseLLM = SlotFillingModels(max_retries=0, timeout=15, first_chunk_timeout=10)
    backup_llms: List[BaseLLM] = []
    output_dir: str = "outputs/"
    two_step: bool = True
    language: str = 'en'
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # if (llm:=kwargs.get('model', '')) == 'claude':
        #     self.plan_llm = ClaudeSonnet37()
    
    async def use_tool(self, user_prompt: str, planning_task: dict = {}, feedback: str = '', hitl_mode: str = '', **kwargs):
        rewrite_question = planning_task.get('rewrite_question', '')
        original_question = rewrite_question or planning_task.get('question', user_prompt)
        final_question = rewrite_question or original_question
        download_link = kwargs.get('download_link', True)
        user = planning_task.get('user', 'unknown')
        object_path = f"planning/{user}/{planning_task.get('id', 'unknown')}"
        self.output_dir = f"outputs/" + object_path
        bucket_name = "noahai-userdata-test"
        approve = kwargs.get('approve', True)
        translations = planning_task.get('translations', {})
        past_feedback = planning_task.get('feedback', [])
        total_feedback = past_feedback + [feedback] if feedback else past_feedback
        if total_feedback or not approve:
            replan_feedback = ['User wants to replan'] if not approve else []
            combo_prompt = {'original_user_prompt': final_question, 'additional_user_prompt': total_feedback + replan_feedback}
            final_question = json.dumps(combo_prompt, separators=(',', ': '), ensure_ascii=False)
        plan = planning_task.get('plan', []) 
        should_run = bool(plan and approve) # user confirmation of tool use
        self.language = kwargs.get('language', '').lower() or planning_task.get('language', '').lower() or 'en'
        hitl_mode = kwargs.get('hitl_mode', '') or planning_task.get('hitl_mode', '') or 'always'
        ret = {'type': 'chat', 'sender': 'assistant', "current_step": planning_task.get('current_step', 0), 
               'version': planning_task.get('version', 0), 'feedback': total_feedback, 'approve': approve,
               'translations': translations, 'plan': plan, 'saveChat': False, "chunkIdx": 0, 'agent': 'planning'}
        if plan and plan[ret['current_step']-1]:
            async for _ret in send_confirm_tool(ret, feedback, approve):
                yield _ret
        elif not plan:
            if feedback:
                async for _ret in send_confirm_tool(ret, feedback, approve):
                    yield _ret
            else:
                async for _ret in send_chat_message(ret, final_question): 
                    yield _ret
        if hitl_mode == 'never':
            should_run = True
        
        planning_schema = PlanningInputSchemaCn if self.language == 'cn' else PlanningInputSchema
        tool_sequence_extraction_template = tool_sequence_extraction_template_cn if self.language == 'cn' else tool_sequence_extraction_template_en
        
        if not plan:
            # try rewrite user original question, first rewrite
            rewrite_results = planning_task.get("rewrite_results", [])
            chunk_idx = len(total_feedback) + len(rewrite_results)
            if not rewrite_question:
                rewrite_body = {
                    "user_prompt": original_question,
                    "history_messages": [],
                    "agent": "mindsearchrewrite",
                    "params": {
                        "language": self.language,
                        "feedbacks": total_feedback,
                        "rewrites": rewrite_results
                    }
                }
                agent = MindSearchRewriteAgent()
                generator = agent.start(**rewrite_body)
                ret['type'] = 'thought'
                ret['chunkIdx'] = chunk_idx
                async for chunk in generator:
                    if not chunk:
                        continue
                    if type(chunk) == dict:
                        latest_chunk = chunk.get('content', '')
                    elif type(chunk) == str:
                        latest_chunk = chunk
                    ret['message'] = latest_chunk + '\n'
                    yield ret

                rewrite_result = json.loads(ret['message'])
                ret['rewrite_result'] = rewrite_result['content']
                chunk_idx += 1
                ret['chunkIdx'] = chunk_idx
                if rewrite_result['processing_type'] == ProcessingType.REWRITE.value:
                    rewrite_question = rewrite_result['content']
                    final_question = rewrite_question
                    ret['rewrite_question'] = rewrite_question
                else:
                    ret['message'] = rewrite_result['content']
                    async for ret in send_agent_status_update(ret, 'waiting'):
                        yield ret
                    return
            if rewrite_question:
                ret['type'] = 'chat'
                planning_prompt = build_planning_prompt(self.language, final_question, ret['plan'][:ret['current_step']-1], ret['plan'], total_feedback+['User wants to replan'], MAX_STEPS=MAX_STEPS)
                
                response_stream = self.plan_llm.stream_call(user_prompt=planning_prompt)
                ret['current_step'] = 0 
                async for chunk in task_with_heartbeat(response_stream, interval=1, stream=True):
                    if not chunk: continue
                    ret['message'] = chunk
                    yield ret
                async for _ret in send_message_and_save(ret):
                    yield _ret
                ret.pop('rewrite_result', None)
                ret.pop('rewrite_question', None)
                
                noah_plan = chunk
                planning_prompt = tool_sequence_extraction_template.format(noah_plan=noah_plan)
                async for _ret in save_and_hide(ret):
                    yield _ret
                ret.pop('message', None)
                planning_format = get_openai_json_schema_v3(planning_schema)
                ret['plan'] = (await self.extract_plan(planning_prompt, ret['plan'][:ret['current_step']-1], planning_format, translations))
                ret['version'] += 1
                ret['type'] = 'planUpdate'
                async for _ret in send_message_and_save(ret):
                    yield _ret
                ret['current_step'] += 1
                
            # if not rewrite_question:
            #     # just rewrite and need user waiting
            #     async for _ret in send_agent_status_update(ret, 'waiting'):
            #         yield _ret
            #     return
        # Replan if user rejects plan
        elif not approve:
            planning_prompt = build_planning_prompt(self.language, final_question, ret['plan'][:ret['current_step']-1], ret['plan'], [feedback]+['User wants to replan'], MAX_STEPS=MAX_STEPS)
            if self.two_step:
                response_stream = self.plan_llm.stream_call(user_prompt=planning_prompt)
                async for chunk in task_with_heartbeat(response_stream, interval=1, stream=True):
                    if not chunk: continue
                    ret['message'] = chunk
                    yield ret
                async for _ret in send_message_and_save(ret):
                    yield _ret
                ret.pop('message', None)
                value = chunk
                planning_prompt = tool_sequence_extraction_template.format(noah_plan=value)
            planning_format = get_openai_json_schema_v3(planning_schema)
            ret['plan'] = ret['plan'][:ret['current_step']-1] + (await self.extract_plan(planning_prompt, ret['plan'][:ret['current_step']-1], planning_format, translations))
            
            ret['version'] += 1
            ret['type'] = 'planUpdate'
            async for _ret in send_message_and_save(ret):
                yield _ret

        if (error:=await check_errors(ret)):
            async for _ret in send_error_message(ret, error):
                yield _ret
            return
        
        if 'params' not in ret['plan'][ret['current_step']-1] and ret['plan'][ret['current_step']-1]['tool'] not in ['Generate-Summary']:
            tool_filling_msg_json = await function_call_with_retry(self.tool_slot_filling, final_question, ret, prior_tool_use=ret['plan'][:ret['current_step']-1], translations=translations, feedback=feedback)
            ret['plan'][ret['current_step']-1]['params'] = clean_args_typesense(tool_filling_msg_json, tool=ret['plan'][ret['current_step']-1]['tool'])
            save_to_file(tool_filling_msg_json, self.output_dir, f"{ret['current_step']}_{ret['plan'][ret['current_step']-1]['tool']}_params.json")
            async for _ret in send_plan_update(ret):
                yield _ret
            
        while should_run:
            async for _ret in send_agent_status_update(ret, 'running'):
                yield _ret
            
            ret['type'] = 'chat'
            # Run the query first to get potential context to select from
            tool_name = ret['plan'][ret['current_step']-1]['tool']
            stream = False
            if tool_name == 'Generate-Summary':
                summary_prompt = build_summary_prompt(final_question, ret['plan'][ret['current_step']-1], ret['plan'][:ret['current_step']-1])
                generator = self.plan_llm.stream_call(user_prompt=summary_prompt)
                stream = True
            if tool_name == 'General-Inference':
                inference_prompt = build_inference_prompt(final_question, ret['plan'][ret['current_step']-1], ret['plan'][:ret['current_step']-1])
                generator = self.plan_llm.stream_call(user_prompt=inference_prompt)
                stream = True
            elif tool_name in ['Medical-Search', 'Web-Search']:
                search_prompt = build_search_prompt(final_question, ret['plan'][ret['current_step']-1], ret['plan'][:ret['current_step']-1])
                step_body = {
                    "user_prompt": search_prompt,
                    "history_messages": [],
                    "agent": "mindsearchofficialsite" if tool_name == "Medical-Search" else "mindsearch",
                    "skip_followup": True,
                    "params":{
                        "language": self.language,
                        "model": "",
                        "enable_rag": True,
                        }
                }
                agent = MindSearchOfficialAgent() if tool_name == "Medical-Search" else MindSearchAgent()
                generator = agent.start_wo_dump(**step_body)
                ret['type'] = 'thought'
            else:
                context_data = await run_query(ret['plan'][ret['current_step']-1], self.output_dir, ret['current_step'], self.language, final_question)
                # Add import for CSV handling if not already at the top

                # Convert the JSON data to CSV format for easier viewing
                if isinstance(context_data, list) and context_data and isinstance(context_data[0], dict):
                    # Handle case where the data is a list of dictionaries
                    csv_data = io.StringIO()
                    fieldnames = context_data[0].keys()
                    writer = csv.DictWriter(csv_data, fieldnames=fieldnames)
                    writer.writeheader()
                    for row in context_data:
                        writer.writerow(row)
                    save_to_file(csv_data.getvalue(), self.output_dir, f"{ret['current_step']}_{ret['plan'][ret['current_step']-1]['tool']}_data.csv")
                save_to_file(context_data, self.output_dir, f"{ret['current_step']}_{ret['plan'][ret['current_step']-1]['tool']}_data.json")
                
                extra_explanation = ''
                if ret['plan'][ret['current_step']-1]['tool'] == "Drug-Analysis" and ret['plan'][ret['current_step']-1]['params'].get('location', []):
                    extra_explanation = f"(Please note that the location is limited to {ret['plan'][ret['current_step']-1]['params']['location']})"
                
                background_prefix = background_prefix_map[ret['plan'][ret['current_step']-1]['tool']] if ret['plan'][ret['current_step']-1]['tool'] in background_prefix_map else ''
                background = f"{background_prefix}{extra_explanation} {context_data}" if context_data else data_empty_prompt_cn if self.language == 'cn' else data_empty_prompt
                if (body_user_prompt:=ret['plan'][ret['current_step']-1].get('params',{}).get('question','')):
                    body_user_prompt += f" to answer the original user question: "
                body_user_prompt += final_question
                total_feedback_prompt = ''
                if total_feedback:
                    total_feedback_prompt = f"Consider the user's feedback: {total_feedback[-1]} and answer:\n"
                step_body = {
                    "user_prompt": f"{total_feedback_prompt}{body_user_prompt} (Do not make assumptions on data, only use whatever context/data has been provided to you.)",
                    "history_messages": [],
                    "agent":"mindsearchworkflowrefer",
                    "skip_followup": True,
                    "params":{
                        "language": self.language,
                        "model": "",
                        "enable_rag": False,
                        "background": background,
                        }
                }
                if ret['plan'][:ret['current_step']-1] and context_data:
                    pattern = r'(?:[#]+\s+[^\n]+\s*)?```vega[\s\S]*?```'
                    # Replace all occurrences with an empty string
                    text = tool_history_to_prompt(ret['plan'][:ret['current_step']-1])
                    cleaned_text = re.sub(pattern, '', text)
                    step_body['params']['background'] += f'\n{cleaned_text}'
                    
                agent = MindSearchWorkflowAgent()
                generator = agent.start_wo_dump(**step_body)
            
            ret['chunkIdx'] = 0
            data = {'last_appearance_end': 0}
            async for chunk in task_with_heartbeat(generator, interval=1, stream=stream):
                if not chunk:
                    continue
                async for ret in process_chunks(chunk, ret, data):
                    yield ret
            
            save_to_file(ret['plan'][ret['current_step']-1]['result'], self.output_dir, f"{ret['current_step']}_{ret['plan'][ret['current_step']-1]['tool']}_response.md")
            
            if ret['current_step'] == len(ret['plan']) and download_link:
                try:
                    async for _ret in send_message_and_save(ret, save=False):
                        yield _ret
                    await upload_archive(self.output_dir, f"{object_path}.zip", bucket_name)
                    ret['attachments_key'] = f"{self.output_dir}.zip"
                    download_link = "\n---\n\n" + ("## 下载链接：[结果与数据]" if self.language.lower() == 'cn' else "## Download link: [Results & Data]") + f"(https://{bucket_name}.obs.cn-south-1.myhuaweicloud.com/{object_path}.zip)"
                    ret['plan'][ret['current_step']-1]['result'] += download_link
                    ret['message'] = download_link
                except:
                    trace = traceback.format_exc()
                    logger.info(f"Error in data upload: {trace}")
            # TODO: remove later
            ret['plan'][ret['current_step']-1]['status'] = 'done'
            if ret.get('message', ''):
                async for _ret in send_message_and_save(ret, save=False):
                    yield _ret
            if isinstance(ret['plan'][ret['current_step']-1]['result'], str):
                full_result = ret['plan'][ret['current_step']-1]['result']
            else:
                full_result = str(ret['plan'][ret['current_step']-1]['result'].get('content', '') or ret['plan'][ret['current_step']-1]['result'])
            ret['message'] = full_result
            if ret['type'] == 'chat':
                pattern = r'```(?:vega|mermaid)[\s\S]*?```'
                matches = list(re.finditer(pattern, ret['message']))
                if matches:
                    start = 0
                    for idx, match in enumerate(matches):
                        _, end = match.span()
                        ret['message'] = full_result[start:end]
                        ret['chunkIdx'] = idx
                        async for _ret in send_message_and_save(ret):
                            yield _ret
                        start = end
                    if full_result[end:]:
                        ret['message'] = full_result[end:]
                        ret['chunkIdx'] = idx + 1
                        async for _ret in send_message_and_save(ret):
                            yield _ret
                else:
                    async for _ret in send_message_and_save(ret):
                        yield _ret
            else:
                async for _ret in send_message_and_save(ret):
                    yield _ret
            ret['chunkIdx'] = 0
            ret.pop('message', '')
            
            ret['type'] = 'planUpdate'
            ret.pop('attachments_key', '')
            ret['plan'][ret['current_step']-1]['status'] = 'done'
            async for _ret in send_message_and_save(ret):
                yield _ret
            # Reflection on plan at penultimate step
            if ret['current_step']+1 == len(ret['plan']) and MAX_STEPS_WITH_REFLECTION > len(ret['plan']) and 'Self-Reflection' not in [tool['tool'] for tool in ret['plan']]:
                ret['current_step'] += 1
                async for _ret in self.self_reflection(ret, final_question):
                    yield _ret
            if len(ret['plan']) > ret['current_step']:
                ret['plan'][ret['current_step']]['status'] = 'doing'
                ret['plan'][ret['current_step']]['startedAt'] = int(time.time())
            ret['current_step'] += 1
            async for _ret in send_message_and_save(ret):
                yield _ret
            if ret['current_step']-1 >= len(ret['plan']):
                should_run = False
                break
            if hitl_mode == 'always':
                should_run = False
            tool_filling_msg_json = await function_call_with_retry(self.tool_slot_filling, final_question, ret, prior_tool_use=ret['plan'][:ret['current_step']-1], translations=translations, feedback=feedback)
            if ret['plan'][ret['current_step']-1]['tool'] in ["Clinical-Trial-Result-Analysis", "Catalyst-Event-Analysis"]:
                tool_filling_msg_json = clean_args_typesense(tool_filling_msg_json)
            elif ret['plan'][ret['current_step']-1]['tool'] == "Drug-Analysis":
                tool_filling_msg_json = clean_args_typesense(tool_filling_msg_json, citeline=True)
                if not tool_filling_msg_json.get('location', []):
                    tool_filling_msg_json['location'] = ['USA', 'China', 'Japan', 'UK', 'France', 'Germany', 'Italy', 'Spain']
            ret['plan'][ret['current_step']-1]['params'] = {k: v for k, v in tool_filling_msg_json.items() if v}
            save_to_file(ret['plan'][ret['current_step']-1]['params'], self.output_dir, f"{ret['current_step']}_{ret['plan'][ret['current_step']-1]['tool']}_params.json")
            
        status = 'waiting' if ret['current_step']-1 < len(ret['plan']) else 'stopped'
        async for _ret in send_agent_status_update(ret, status):
            yield _ret

    async def tool_slot_filling(self, original_question_and_feedback, ret, prior_tool_use=None, translations={}, feedback=''):
        try:
            tool_name = ret['plan'][ret['current_step']-1]['tool']
            tool_info = tool_mapping[tool_name]
            prompt = original_question_and_feedback
            if tool_info['prompt']:
                prompt = tool_info['prompt']
            tool_schema = tool_info['schema'] if self.language == 'en' else tool_info['schema_cn']
            planning_format = get_openai_json_schema_v3(tool_schema)
            if prior_tool_use:
                if type(prior_tool_use) == list:
                    prior_tool_use_names = [step.get('tool','') for step in prior_tool_use]
                prompt += f"\nPrevious tool uses: {prior_tool_use_names}\n"
            if (query_params:=ret['plan'][ret['current_step']-1].get('query_params', '')):
                prompt += f"\nCurrent tool query params description: {query_params}\n"
            if (reason:=ret['plan'][ret['current_step']-1].get('reason', '')):
                prompt += f"\nThe goal/reason for choosing this tool: {reason}"
                if feedback: prompt += f'\nConsider the user feedback: {feedback}'
                else: prompt += f' (to answer the original user question: {original_question_and_feedback})'

            if translations:
                prompt += f'\nHere are some translation results you can consider when performing the function call: {str(translations)}'
            # prompt += '\n**Important**: You must return the result in function calling format according to the schema provided.'
            # prompt += f'\n{function_call_note}'
            function_name = planning_format[0]['function']['name']
            response = await self.slot_filling_llm(user_prompt=prompt, tools=planning_format, tool_choice={"type": "function", "function": {"name": function_name}}, temperature=0.3, max_tokens=8192)
            return response
        except Exception as e:
            logger.info(f"Error in tool_slot_filling {e}")
            raise e
        
    async def extract_plan(self, planning_prompt, prior_tool_use = [], planning_format = None, translations={}):
        kwargs = {}
        if planning_format:
            function_name = planning_format[0]['function']['name']
            kwargs['tool_choice'] = {"type": "function", "function": {"name": function_name}}
            result = await function_call_with_retry(self.plan_extraction_llm, user_prompt=planning_prompt, tools=planning_format, planning=True, temperature=0.3, max_tokens=8192, **kwargs)
        sequence = result.get('planned_sequence', [])[:MAX_STEPS-len(prior_tool_use)]
        _translations = result.get('translated_keywords', []) or []
        for t in _translations:
            if t['keyword'] not in translations:
                translations[t['keyword']] = t['translated_keyword']
        for step in sequence:
            step['status'] = 'todo' 
            step['startedAt'] = int(time.time())
        if sequence:
            sequence[0]['status'] = 'doing'
        return sequence
        
    async def self_reflection(self, ret, user_question):
        reason = "评判规划执行结果" if self.language == 'cn' else "Evaluate plan execution results"
        reflection_tool = {"reason": reason, "tool": "Self-Reflection"}
        
        ret['plan'] = ret['plan'][:ret['current_step']-1] + [{**reflection_tool.copy(), "status": "doing", "startedAt": int(time.time())}] + ret['plan'][ret['current_step']-1:]
        async for _ret in send_plan_update(ret):
            yield _ret
            
        async for _ret in send_agent_status_update(ret, 'running'):
            yield _ret
            
        reflection_instructions_prompt = reflection_instructions.format(additional_step_count=MAX_STEPS_WITH_REFLECTION-ret['current_step']-1)
            
        current_plan = list(ret['plan'])
        for step in current_plan:
            step.pop('result', None)
            step.pop('params', None)
        reflection_prompt = reflection_template.format(
            current_date=datetime.now().strftime('%Y-%m-%d'), 
            instructions_prompt=reflection_instructions_prompt,
            user_prompt=user_question,
            current_plan=current_plan,
            prior_knowledge=tool_history_to_prompt(ret['plan'][:ret['current_step']-1]),
            next_steps=ret['plan'][ret['current_step']:]
            )
        
        ret['type'] = 'chat'
        response_stream = self.plan_llm.stream_call(user_prompt=reflection_prompt)
        async for chunk in task_with_heartbeat(response_stream, interval=1, stream=True):
            if not chunk: continue
            ret['message'] = chunk
            yield ret
        ret['plan'][ret['current_step']-1]['result'] = reflection_message = ret['message']
        save_to_file(ret['plan'][ret['current_step']-1]['result'], self.output_dir, f"{ret['current_step']}_{ret['plan'][ret['current_step']-1]['tool']}_response.md")
        async for _ret in send_message_and_save(ret):
            yield _ret
        
        reflection_extraction_template = reflection_extraction_template_en if self.language == 'en' else reflection_extraction_template_cn
        reflection_extraction_prompt = reflection_extraction_template.format(reflection=reflection_message)
        
        reflection_schema = get_openai_json_schema_v3(ReflectionSchema)
        tool_choice = {"type": "function", "function": {"name": reflection_schema[0]['function']['name']}}
        slot_fill_result = await function_call_with_retry(self.slot_filling_llm, user_prompt=reflection_extraction_prompt, tools=reflection_schema, tool_choice=tool_choice, planning=True, temperature=0.3, max_tokens=8192)

        additional_steps = slot_fill_result.get('additional_steps', [])[:MAX_STEPS_WITH_REFLECTION-ret['current_step']-1]
        if not additional_steps:
            additional_steps = [{'tool': 'Generate-Summary', 'status': 'todo', 'startedAt': int(time.time()), 'reason':'summarize the results of the previous steps and answer the user question'}]
        while additional_steps and additional_steps[-1].get('tool', '') == 'General-Inference':
            additional_steps.pop()
        for step in additional_steps:
            step['status'] = 'todo' 
            step['startedAt'] = int(time.time())
            
        ret['plan'] = ret['plan'][:ret['current_step']] + additional_steps + ret['plan'][ret['current_step']:]
        ret['plan'][ret['current_step']-1]['status'] = 'done'
        ret['type'] = 'planUpdate'
        ret.pop('message', None)
        ret['version'] += 1