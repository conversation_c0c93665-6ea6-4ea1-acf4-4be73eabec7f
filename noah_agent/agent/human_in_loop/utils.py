import asyncio
import json
import os
import io
import re
import shutil
import logging
import time
import traceback
import glob
import pickle
from utils.redis_client import create_engines
cache = create_engines(decode_responses=False)

logger = logging.getLogger(__name__)

async def upload_archive(output_dir, object_path, bucket_name, source='azure'):
    if source == 'hw':
        from utils.obs.client import upload_file
    else:
        from utils.azure.blob_client import upload_file
    # Save report and outputs to zip file
    zip_path = f"{output_dir}.zip"
    if not os.path.exists(output_dir + '/data'):
        os.makedirs(output_dir + '/data', exist_ok=True)
    shutil.make_archive(output_dir, 'zip', output_dir)
    logger.info(f"Output saved to {zip_path}")
    
    for _ in range(3):
        res = upload_file(bucket_name, object_path, zip_path)
        if res: 
            logger.info(f"File {zip_path} uploaded successfully")
            # Delete zip and original folder when upload is successful
            try:
                os.remove(zip_path)  # Delete the zip file
                shutil.rmtree(output_dir)  # Delete the original folder
                logger.info(f"Cleaned up {zip_path} and {output_dir}")
            except Exception as e:
                logger.error(f"Failed to clean up files: {str(e)}")
            break
        await asyncio.sleep(3)
    else:
        logger.error(f"Failed to upload {zip_path}")

async def send_message_and_save(ret, save=True):
    new_ret = ret.copy()
    # for _ in range(0):
    #     yield new_ret
    #     await asyncio.sleep(0.1)
    if save:
        new_ret['saveChat'] = True
        yield new_ret
        
async def send_editable_message(ret, message, editable=True):
    new_ret = ret.copy()
    new_ret['sender'] = 'assistant'
    new_ret['type'] = 'chat'
    new_ret['editable'] = editable
    new_ret['message'] = message
    async for new_ret in send_message_and_save(new_ret):
        yield new_ret
        
        
async def save_rewrite_question(ret, rewrite_question, editable=True):
    new_ret = ret.copy()
    new_ret['sender'] = 'assistant'
    new_ret['type'] = 'chat'
    new_ret['editable'] = editable
    new_ret['message'] = rewrite_question
    new_ret['rewrite_question'] = rewrite_question
    async for new_ret in save_and_hide(new_ret, hide=True):
        yield new_ret
        
async def save_and_hide(ret, hide=True):
    new_ret = ret.copy()
    new_ret['saveChat'] = True
    new_ret['hide'] = hide
    yield new_ret

async def send_confirm_tool(ret, feedback, approve):
    new_ret = ret.copy()
    new_ret['type'] = 'confirmTool'
    new_ret['sender'] = 'user'
    new_ret['message'] = feedback
    new_ret['accept'] = approve
    async for new_ret in send_message_and_save(new_ret):
        yield new_ret
        
async def send_chat_message(ret, final_question):
    new_ret = ret.copy()
    new_ret['type'] = 'chat'
    new_ret['sender'] = 'user'
    new_ret['message'] = final_question
    async for new_ret in send_message_and_save(new_ret):
        yield new_ret
        
async def send_agent_status_update(ret, status):
    new_ret = ret.copy()
    new_ret['type'] = 'statusUpdate'
    new_ret['sender'] = 'assistant'
    new_ret['agentStatus'] = status
    async for new_ret in send_message_and_save(new_ret):
        yield new_ret
        
async def send_plan_update(ret):
    new_ret = ret.copy()
    new_ret['type'] = 'planUpdate'
    new_ret['message'] = ""
    async for new_ret in send_message_and_save(new_ret):
        yield new_ret
        
async def send_error_message(ret, error_message):
    new_ret = ret.copy()
    new_ret["error"] = error_message
    async for new_ret in send_message_and_save(new_ret):
        yield new_ret
        
async def check_errors(ret):
    if not ret['plan']:
        return "Planning failed"
    if ret['current_step']-1 >= len(ret['plan']):
        return "All steps completed"
    return None

async def task_with_heartbeat(gen, interval: float = 0.3, stream=False):
    r"""
    Since fetch web page contents may cost very long time. Send heartbeat at the same time to avoid connection close.
    """
    try:
        buffer = io.StringIO()
            
        newest_chunk = None
        start_time = time.time()
        async def write_buffer():
            nonlocal newest_chunk
            async for chunk in gen:
                if not chunk:
                    continue
                if stream:
                    buffer.write(chunk)
                else:
                    if type(chunk) == str:
                        newest_chunk = chunk
                    elif type(chunk) == dict:
                        newest_chunk = chunk
        task = asyncio.create_task(write_buffer())
        shielded = asyncio.shield(task)

        while not task.done():
            if stream:
                yield buffer.getvalue()
            elif newest_chunk:
                yield newest_chunk
            await asyncio.sleep(interval)
        
        await shielded
        end_time = time.time()
        if stream:
            yield buffer.getvalue()
        elif newest_chunk:
            yield newest_chunk
        logger.info(f"[_task_with_heartbeat]{callable} cost time total {end_time - start_time}s")
    except Exception as e:
        traceback.print_exc()
        raise Exception(f"Task {gen.__name__} with heartbeat failed: {str(e)}")
    
async def process_chunks(chunk, ret, data):
    try:
        last_appearance_end = data['last_appearance_end']
        ret['plan'][ret['current_step']-1]['result'] = chunk
        if type(chunk) == dict:
            content = chunk.get('content', '')
        else:
            content = chunk
        if ret['type'] == 'thought' and type(chunk) == dict and content:
            async for _ret in send_message_and_save(ret, save=True):
                yield _ret
            ret['type'] = 'chat'
        matches = None
        if ret['type'] == 'chat':
            code_block_ranges = [(0,0)]
            # Look for code blocks in the chunk
            pattern = r'```(?:vega|mermaid)[\s\S]*?```'
            matches = list(re.finditer(pattern, content[last_appearance_end:]))
            
        # Store index ranges of all matches
        if matches:
            code_block_ranges = [(m.start(), m.end()) for m in matches]
            _last_appearance_end = last_appearance_end
            for code_block_range in code_block_ranges:
                appearance_end = code_block_range[1]
                
                latest_content = content[_last_appearance_end:_last_appearance_end+appearance_end]
                ret['message'] = latest_content
                async for _ret in send_message_and_save(ret, save=False):
                    yield _ret
                ret['chunkIdx'] += 1
                data['last_appearance_end'] = _last_appearance_end + appearance_end
            # ret['saveChat'] = False
        if ret['type'] == 'thought':
            latest_content = json.dumps(chunk, ensure_ascii=False) + '\n'
        else:
            latest_content = content[last_appearance_end:]
        ret['message'] = latest_content
        if ret['message']:
            yield ret
    except Exception as e:
        trace = traceback.format_exc()
        logger.info(f"Error in chunk processing: {trace}")
        raise e
    
def convert_md_to_docx(dir):
    from agent.human_in_loop.md2docx import MarkdownToWordTool
    markdown_to_word_tool = MarkdownToWordTool()
    input_dir = output_dir = dir
    # 获取所有 Markdown 文件
    md_files = glob.glob(os.path.join(input_dir, "*.md"))
    
    if not md_files:
        print(f"在 {input_dir} 目录下未找到 Markdown 文件")
        return
    
    print(f"找到 {len(md_files)} 个 Markdown 文件，开始处理...")
    
    # 处理每个 Markdown 文件
    for md_file in md_files:
        # 从输入文件名获取基本文件名
        base_name = os.path.basename(md_file)
        output_filename = os.path.splitext(base_name)[0] + ".docx"
        output_path = os.path.join(output_dir, output_filename)
        
        print(f"正在处理: {md_file} -> {output_path}")
        
        # 使用run方法调用工具
        try:
            result = markdown_to_word_tool.run(
                input_path=md_file,
                output_path=output_path
            )
            
            # 打印结果
            print(result)
            print("-" * 50)
            # Delete the markdown file after successful conversion
            if os.path.exists(output_path):
                try:
                    os.remove(md_file)
                    print(f"Successfully deleted the original markdown file: {md_file}")
                except Exception as delete_error:
                    print(f"Warning: Failed to delete {md_file}: {str(delete_error)}")
        except Exception as e:
            print(f"处理 {md_file} 时出错: {str(e)}")
            print("-" * 50)
            raise e
    
    print("所有文件处理完成！")
    
async def wait_for_confirm_tool_input(self, seconds=10):
    """
    Wait for user to confirm the tool input.
    """
    stop_auto_run = None
    for _ in range(seconds):
        stop_auto_run = cache.get(f':1:{self.thread_id}-stop-auto-run')
        if stop_auto_run:
            break
        await asyncio.sleep(1)
    else:
        stop_auto_run = cache.get(f':1:{self.thread_id}-stop-auto-run')
    if stop_auto_run:
        stop_auto_run = pickle.loads(stop_auto_run)
        self.auto_run_stopped = stop_auto_run 
        cache.delete(f':1:{self.thread_id}-stop-auto-run')
        return not stop_auto_run
    return None
        
async def wait_for_interrupt_input(self, thread_id):
    """
    Wait for user to confirm the tool input.
    """
    stop_run = cache.get(f':1:{thread_id}-stop')
    if stop_run:
        stop_run = pickle.loads(stop_run)
        cache.delete(f':1:{thread_id}-stop')
        self.stopped = True
        return stop_run
    return False