import asyncio
from datetime import datetime
import io
import os
import hashlib


import re
import shutil
import time
import traceback
from typing import Callable, List, Type
from agent.human_in_loop.query import run_query
from agent.explore.mindsearch_workflow_agent import MindSearchWorkflowAgent
from agent.explore.mindsearch_agent_v2 import MindSearchWebHitlAgent
from agent.explore.mindsearch_rewrite_agent import MindSearchRewriteAgent
from agent.explore.mindsearch_rewrite_agent_v2 import MindSearchRewriteAgentV2
from agent.explore.schema import ProcessingType
from llm.gcp_models import <PERSON><PERSON>onnet<PERSON>, <PERSON><PERSON>onnet37Bypass, Claude<PERSON><PERSON><PERSON>37<PERSON>hinking, <PERSON><PERSON><PERSON><PERSON>37<PERSON>hinkingBypass, Gemini25Pro
from llm.composite_models import SlotFillingModels
from agent.human_in_loop.utils import *
from agent.human_in_loop.constants import search_routing
from utils.human_in_loop.helpers import *
from utils.core.get_json_schema import get_openai_json_schema_v3
from tools.human_in_loop.planning.schema import *
from tools.human_in_loop.planning.schema_cn import *
from tools.human_in_loop.planning.prompt import *
import urllib.parse

from agent.core.preset import AgentPreset
from llm.azure_models import DiagnosisModels, GPT4o, Ada
from llm.base_model import BaseLLM
from llm.deepseek_models import ClaudeThenDeepseekChat, CompositeDeepseekChat, CompositeDeepseekReasoner, ClaudeThenDeepseekChat2
from llm.composite_models import CompositeHitlFinal
from utils.clinical_utils.clean_args_typesense import clean_args_typesense
import json
import csv
import logging

logger = logging.getLogger(__name__)

MAX_STEPS = 4
MAX_STEPS_WITH_REFLECTION = 15
tool_mapping = {
    # "NCCN-Guidelines": {"schema": NCCNGuidelinesInputSchema, "prompt": nccn_slot_filling_prompt},
    "Medical-Diagnosis": {"schema": GeneralInferenceInputSchema, "prompt": general_inference_slot_filling_prompt},  # Translated from "诊疗问题"
    "General-Inference": {"schema": GeneralInferenceInputSchema, "prompt": general_inference_slot_filling_prompt},
    # "PubMed-Search": {"schema": MedicalSearchInputSchema, "prompt": medical_search_slot_filling_prompt},
    "Medical-Search": {"schema": MedicalSearchInputSchema, "prompt": medical_search_slot_filling_prompt},
    "Web-Search": {"schema": WebSearchInputSchema, "prompt": web_search_slot_filling_prompt},
    "Finance-Search": {"schema": FinanceSearchInputSchema, "prompt": finance_search_slot_filling_prompt},
    "Patent-Search": {"schema": PatentSearchInputSchema, "prompt": patent_search_slot_filling_prompt},
    "News-Search": {"schema": NewsSearchInputSchema, "prompt": news_search_slot_filling_prompt},
    "Clinical-Trial-Result-Analysis": {"schema": ClinicalResultsInputSchema, "prompt": clinical_trial_results_slot_filling_prompt},
    "Drug-Analysis": {"schema": DrugCompetitionLandscapeInputSchema, "prompt": drug_competition_landscape_slot_filling_prompt},
    "Catalyst-Event-Analysis": {"schema": CatalystSearchInputSchema, "prompt": catalyst_search_slot_filling_prompt},
}

tool_mapping_cn = {
    # "NCCN-Guidelines": {"schema": NCCNGuidelinesInputSchema, "prompt": nccn_slot_filling_prompt},
    "Medical-Diagnosis": {"schema": GeneralInferenceInputSchemaCn, "prompt": general_inference_slot_filling_prompt},  # Translated from "诊疗问题"
    "General-Inference": {"schema": GeneralInferenceInputSchemaCn, "prompt": general_inference_slot_filling_prompt},
    # "PubMed-Search": {"schema": MedicalSearchInputSchemaCn, "prompt": medical_search_slot_filling_prompt},
    "Medical-Search": {"schema": MedicalSearchInputSchemaCn, "prompt": medical_search_slot_filling_prompt},
    "Web-Search": {"schema": WebSearchInputSchemaCn, "prompt": web_search_slot_filling_prompt},
    "Finance-Search": {"schema": FinanceSearchInputSchema, "prompt": finance_search_slot_filling_prompt},
    "Patent-Search": {"schema": PatentSearchInputSchema, "prompt": patent_search_slot_filling_prompt},
    "News-Search": {"schema": NewsSearchInputSchema, "prompt": news_search_slot_filling_prompt},
    "Clinical-Trial-Result-Analysis": {"schema": ClinicalResultsInputSchemaCn, "prompt": clinical_trial_results_slot_filling_prompt},
    "Drug-Analysis": {"schema": DrugCompetitionLandscapeInputSchemaCn, "prompt": drug_competition_landscape_slot_filling_prompt},
    "Catalyst-Event-Analysis": {"schema": CatalystSearchInputSchemaCn, "prompt": catalyst_search_slot_filling_prompt},
}

tool_mapping_rl = {
    # "NCCN-Guidelines": {"schema": NCCNGuidelinesInputSchema, "prompt": nccn_slot_filling_prompt},
    "Medical-Diagnosis": {"schema": GeneralInferenceInputSchemaCn, "prompt": general_inference_slot_filling_prompt},  # Translated from "诊疗问题"
    "General-Inference": {"schema": GeneralInferenceInputSchemaCn, "prompt": general_inference_slot_filling_prompt},
    # "PubMed-Search": {"schema": MedicalSearchInputSchemaCn, "prompt": medical_search_slot_filling_prompt},
    "Medical-Search": {"schema": MedicalSearchInputSchemaCn, "prompt": medical_search_slot_filling_prompt},
    "Web-Search": {"schema": WebSearchInputSchemaCn, "prompt": web_search_slot_filling_prompt},
    "Finance-Search": {"schema": FinanceSearchInputSchema, "prompt": finance_search_slot_filling_prompt},
    "Patent-Search": {"schema": PatentSearchInputSchema, "prompt": patent_search_slot_filling_prompt},
    "News-Search": {"schema": NewsSearchInputSchema, "prompt": news_search_slot_filling_prompt},

    "Clinical-Trial-Result-Analysis": {"schema": ClinicalTrialResultAnalysisInputSchema, "prompt": database_tool_slot_filling_template},
    "Drug-Analysis": {"schema": DrugAnalysisInputSchema, "prompt": database_tool_slot_filling_template},
    "Catalyst-Event-Analysis": {"schema": CatalystEventAnalysisInputSchema, "prompt": database_tool_slot_filling_template},   
}

tool_mapping_rl_cn = {
    # "NCCN-Guidelines": {"schema": NCCNGuidelinesInputSchema, "prompt": nccn_slot_filling_prompt},
    "Medical-Diagnosis": {"schema": GeneralInferenceInputSchemaCn, "prompt": general_inference_slot_filling_prompt},  # Translated from "诊疗问题"
    "General-Inference": {"schema": GeneralInferenceInputSchemaCn, "prompt": general_inference_slot_filling_prompt},
    # "PubMed-Search": {"schema": MedicalSearchInputSchemaCn, "prompt": medical_search_slot_filling_prompt},
    "Medical-Search": {"schema": MedicalSearchInputSchemaCn, "prompt": medical_search_slot_filling_prompt},
    "Web-Search": {"schema": WebSearchInputSchemaCn, "prompt": web_search_slot_filling_prompt},
    "Finance-Search": {"schema": FinanceSearchInputSchema, "prompt": finance_search_slot_filling_prompt},
    "Patent-Search": {"schema": PatentSearchInputSchema, "prompt": patent_search_slot_filling_prompt},
    "News-Search": {"schema": NewsSearchInputSchema, "prompt": news_search_slot_filling_prompt},

    "Clinical-Trial-Result-Analysis": {"schema": ClinicalTrialResultAnalysisInputSchema, "prompt": database_tool_slot_filling_template},
    "Drug-Analysis": {"schema": DrugAnalysisInputSchema, "prompt": database_tool_slot_filling_template},
    "Catalyst-Event-Analysis": {"schema": CatalystEventAnalysisInputSchema, "prompt": database_tool_slot_filling_template},   
}
# output_summary_schema = get_openai_json_schema_v3(OutputSummarySchema)

class PlanningAgent(AgentPreset):
    llm: BaseLLM = CompositeDeepseekChat(max_retries=0, timeout=15, first_chunk_timeout=10)
    plan_llm: BaseLLM = ClaudeThenDeepseekChat(max_retries=0, timeout=15, first_chunk_timeout=10)
    plan_llm_2: BaseLLM = ClaudeThenDeepseekChat2(max_retries=0, timeout=15, first_chunk_timeout=10)
    summary_llm: BaseLLM = CompositeHitlFinal()
    o3_llm: BaseLLM = DiagnosisModels(max_retries=0, timeout=15, first_chunk_timeout=10)
    plan_extraction_llm: BaseLLM = SlotFillingModels(max_retries=0, timeout=45, first_chunk_timeout=10)
    slot_filling_llm: BaseLLM = SlotFillingModels(max_retries=0, timeout=45, first_chunk_timeout=10)
    backup_llms: List[BaseLLM] = []
    output_dir: str = "outputs/"
    two_step: bool = True
    language: str = 'en'
    thread_id: str = ''
    stopped: bool = False
    auto_run_stopped: bool = False 
    database_rl: bool = False
    
    async def use_tool(self, user_prompt: str, history_messages: List[dict] = [], planning_task: dict = {}, feedback: str = '', hitl_mode:str='', **kwargs):
        question = planning_task.get('question', user_prompt) or ''
        rewrite_question = planning_task.get('rewrite_question', '')
        original_question = rewrite_question or question
        rewrite_results = planning_task.get("rewrite_results", [])
        final_question = rewrite_question or original_question
        body = {
            "history_messages": history_messages,
            "user_prompt": final_question
        }
        download_link = kwargs.get('download_link', True)
        user = planning_task.get('user', 'unknown')
        task_id = planning_task.get('id', 'unknown')
        self.thread_id = planning_task.get('thread_id', 'unknown')
        question_prefix = question[:20].replace(' ', '_')
        editable = planning_task.get('editable', None)
        
        safe_prefix = urllib.parse.quote(question_prefix)
        object_path = f"planning/{user}/{task_id}/{question_prefix}..._{datetime.now().strftime('%Y%m%d_%H%M')}_NoahAI"
        encoded_object_path = f"planning/{user}/{task_id}/{safe_prefix}..._{datetime.now().strftime('%Y%m%d_%H%M')}_NoahAI"
        self.output_dir = f"outputs/" + object_path
        STORAGE_DEST = 'azure'
        bucket_name = "noahai-userdata-test" if STORAGE_DEST =='hw' else 'nudata'
        approve = kwargs.get('approve', None)
        should_replan = (approve is False)
        past_feedback = planning_task.get('feedback', [])
        total_feedback = past_feedback + [feedback] if feedback else past_feedback
        if total_feedback or should_replan:
            replan_feedback = ['User wants to replan'] if should_replan else []
            combo_prompt = {'original_user_prompt': final_question, 'additional_user_prompt': total_feedback + replan_feedback}
            body['user_prompt'] = json.dumps(combo_prompt, separators=(',', ': '), ensure_ascii=False)
        self.language = kwargs.get('language', '').lower() or planning_task.get('language', '').lower() or 'en'
        plan = planning_task.get('plan', []) 
        current_tool = {}
        current_step = planning_task.get('current_step', 0)
        if not current_step and plan:
            current_step = 1
        hitl_mode = hitl_mode or planning_task.get('hitl_mode', '') or 'never'
        prev_tool_uses = planning_task.get('tool_uses', [])
        if prev_tool_uses and not prev_tool_uses[-1].get('result', '') and prev_tool_uses[-1].get('tool', '') != 'User-Question':
            current_tool = prev_tool_uses[-1]
        if not current_tool:
            if plan and current_step <= len(plan):
                current_tool = plan[current_step-1].copy()
        should_run = bool(plan and not should_replan) # user confirmation of tool use
        ret = {'tool_uses': [], 'type': 'chat', "agent": "planning", "hitl_mode": hitl_mode, 'sender': 'assistant',
               "current_step": current_step, "current_tool": current_tool, 'feedback': total_feedback}
        if current_tool and current_tool.get('status', 'error') != 'error':
            async for _ret in send_confirm_tool(ret, feedback, should_run):
                yield _ret
        elif not plan:
            if feedback != '' and editable is not False:
                # use same chunk id
                ret['chunkIdx'] = len(rewrite_results) - 1
                ret['message'] = feedback
                ret['stepQuestion'] = rewrite_results[-1]
                async for _ret in send_agent_status_update(ret, 'running'):
                    yield _ret
                ret.pop('stepQuestion')

            elif editable is None:
                ret['chunkIdx'] = len(rewrite_results) - 1
                ret['type'] = 'chat'
                ret['sender'] = 'user'
                ret['message'] = final_question
                ret['language'] = self.language
                async for _ret in send_message_and_save(ret):
                    yield _ret

            ret.pop('language', '')
            ret.pop('message', None)
            ret['sender'] = 'assistant'            
        if hitl_mode == 'never':
            should_run = True
        ret['saveChat'] = False
        
        if self.language == 'cn':
            planning_schema = PlanningInputSchemaCn
            tool_sequence_extraction_template = tool_sequence_extraction_template_cn
        else:
            planning_schema = PlanningInputSchema
            tool_sequence_extraction_template = tool_sequence_extraction_template_en
        
        if not plan or len(plan) <= 1: # if current_tool != plan[0].get('tool')
            # try rewrite user original question, first rewrite

            chunk_idx = len(rewrite_results)

            if not rewrite_question:
                rewrite_body = {
                    "user_prompt": original_question,
                    "history_messages": [],
                    "agent": "mindsearchrewrite",
                    "params": {
                        "language": self.language,
                        "feedbacks": total_feedback,
                        "rewrites": rewrite_results,
                        "tool_use_context": tool_history_to_prompt(prev_tool_uses)
                    }
                }
                agent = MindSearchRewriteAgentV2()
                generator = agent.start(**rewrite_body)
                ret['type'] = 'simpleThought'
                ret['chunkIdx'] = chunk_idx
                async for chunk in generator:
                    if not chunk:
                        continue
                    if type(chunk) == dict:
                        latest_chunk = chunk.get('content', '')
                    elif type(chunk) == str:
                        latest_chunk = chunk
                    ret['message'] = latest_chunk
                    yield ret
                if self.stopped:
                    return

                rewrite_result = json.loads(ret['message'])
                ret['chunkIdx'] = chunk_idx
                if rewrite_result['processing_type'] == ProcessingType.REWRITE.value:
                    rewrite_question = rewrite_result['content']
                    body['user_prompt'] = rewrite_question
                    ret['rewrite_question'] = rewrite_question
                    total_feedback = ret['feedback'] = []
                    # Rewrite Hide: If we want to hide rewrite result, we need to wait one second for frontend udate.
                    # await asyncio.sleep(1) 
                    async for _ret in send_editable_rewrite_question(ret, rewrite_question, True):
                        yield _ret
                    return
                else:
                    ret['rewrite_result'] = rewrite_result['content']
                    ret['message'] = rewrite_result['content']
                    ret['type'] = 'statusUpdate'
                    ret['agentStatus'] = 'waitingFeedback'
                    async for _ret in send_message_and_save(ret):
                        yield _ret

            if rewrite_question:
                logger.info(f'Feedback rewrite question: {rewrite_question}')
                # Rewrite Hide: this condition is to display rewrite result
                ret.pop('chunkIdx', None)
                ret['type'] = 'planUpdate'
                ret['current_step'] = current_step = 1
                plan_reason = "设计工具序列以处理用户请求" if self.language == 'cn' else "Design tool sequence to handle user prompt"
                ret['current_tool'] = {"tool": "Plan-Sequence", 'status': 'doing', 'startedAt': int(time.time()), "reason": plan_reason}
                ret['plan'] = plan = [ret['current_tool']]
                async for _ret in send_message_and_save(ret):
                    yield _ret

                if approve is not True or not editable:
                    # async for _ret in send_agent_status_update(ret, 'running'):
                    #     yield _ret
                    ret['type'] = 'chat'
                    planning_prompt = build_planning_prompt(self.language, body['user_prompt'], prev_tool_uses, plan, total_feedback, MAX_STEPS=MAX_STEPS)
                    response_stream = self.plan_llm_2.stream_call(user_prompt=planning_prompt)
                    async for chunk in self._task_with_heartbeat(response_stream, interval=1, stream=True):
                        if not chunk: continue
                        ret['message'] = chunk
                        yield ret
                    tool_use = ret['current_tool'].copy()
                    tool_use['result'] = ret['message']
                    if prev_tool_uses: 
                        if prev_tool_uses[-1]['tool'] != 'Plan-Sequence':
                            prev_tool_uses.append(tool_use)
                        else:
                            prev_tool_uses[-1]['result'] = ret['message']
                        ret['tool_uses'] = prev_tool_uses
                    else:
                        prev_tool_uses.append(tool_use)
                        ret['tool_uses'] = prev_tool_uses
                    async for _ret in send_message_and_save(ret):
                        yield _ret
                        
                    ret.pop('message', None)
                    ret.pop('chunkIdx', '')
                    
                    countdown_seconds = 30 # Let user have enough time to read plan detail
                    async for _ret in send_agent_status_update(ret, 'waiting', countdown_seconds=countdown_seconds):
                        yield _ret
                    if (await wait_for_confirm_tool_input(self, seconds=countdown_seconds+1)) is False:
                        return
                    async for _ret in send_confirm_tool(ret, None, True):
                        yield _ret
                    async for _ret in send_agent_status_update(ret, 'running'):
                        yield _ret
                
                noah_plan = chunk if approve is not True else prev_tool_uses[-1]['result']
                save_to_file(noah_plan, self.output_dir, f"{current_step-1}_Plan-Sequence_NoahAI.md")
                planning_text = tool_sequence_extraction_template.format(noah_plan=noah_plan)
                planning_format = get_openai_json_schema_v3(planning_schema)
                plan[current_step-1]['status'] = 'done'
                current_step += 1
                plan += (await self.extract_plan(planning_text, current_step, planning_format))
                ret['current_step'] = current_step
                current_tool = plan[current_step-1]
                ret['plan'] = plan
                ret['type'] = 'planUpdate'
                prev_tool_uses[-1]['status'] = 'done'
                ret['tool_uses'] = prev_tool_uses
                async for _ret in send_message_and_save(ret):
                    yield _ret
            
        ret.pop("plan", None)
        
        if not rewrite_question:
            # just rewrite and need user waiting
            return

        if not plan:
            ret["error"] = "Planning failed"
            yield ret
            return
        
        if current_step-1 >= len(plan):
            ret["error"] = "All steps completed"
            yield ret
            return
        
        if not current_tool:
            current_tool = plan[current_step-1].copy()
        
        if 'params' not in current_tool and current_tool['tool'] not in ['Generate-Summary', 'Self-Reflection', 'User-Question', 'Plan-Sequence', 'General-Inference',]: 
            # TODO remove database query tool
            #'Drug-Analysis', 'Catalyst-Event-Analysis', 'Clinical-Trial-Result-Analysis']:
            tool_filling_msg_json = await function_call_with_retry(self.tool_slot_filling, body, current_tool, data=prev_tool_uses, feedback=feedback)
            params_dict = {"original_params": tool_filling_msg_json.copy()}
            tool_filling_msg_json_cleaned = None
            if not self.database_rl:
                if current_tool['tool'] in ["Clinical-Trial-Result-Analysis", "Catalyst-Event-Analysis"]:
                    tool_filling_msg_json_cleaned = clean_args_typesense(tool_filling_msg_json)
                elif current_tool['tool'] == "Drug-Analysis":
                    tool_filling_msg_json_cleaned = clean_args_typesense(tool_filling_msg_json, citeline=True)
                    if not tool_filling_msg_json_cleaned.get('location', []):
                        tool_filling_msg_json_cleaned['location'] = ['USA', 'China', 'Japan', 'UK', 'France', 'Germany', 'Italy', 'Spain']
            params_dict["matched_params"] =  tool_filling_msg_json_cleaned or tool_filling_msg_json
            save_to_file(params_dict, self.output_dir + '_params', f"{current_step-1}_{current_tool['tool']}_params.json")
            current_tool['params'] = params_dict["matched_params"]
            
        ret.pop('rewrite_question', None)
        ret.pop('rewrite_result', None)
        ret['current_tool'] = current_tool
                
        while should_run:
            async for _ret in send_agent_status_update(ret, 'running'):
                yield _ret
            ret['type'] = 'chat'
            # Run the query first to get potential context to select from
            tool_name = current_tool['tool']
            stream = False
            if tool_name == 'Self-Reflection':
                async for _ret in self.self_reflection(ret, final_question, prev_tool_uses, current_tool, current_step, plan, total_feedback, approve):
                    yield _ret
                approve = None
                if self.auto_run_stopped:
                    return
                plan = ret['plan']
            else:
                if tool_name == 'Generate-Summary':
                    summary_prompt = build_summary_prompt(body['user_prompt'], current_tool, prev_tool_uses)
                    generator = self.summary_llm.stream_call(user_prompt=summary_prompt)
                    stream = True
                elif tool_name in ['General-Inference', 'Medical-Diagnosis']:
                    inference_prompt = build_inference_prompt(body['user_prompt'], current_tool, prev_tool_uses)
                    if tool_name == 'Medical-Diagnosis':
                        generator = self.o3_llm.stream_call(user_prompt=inference_prompt)
                    else:
                        generator = self.plan_llm.stream_call(user_prompt=inference_prompt)
                    stream = True
                elif tool_name in ['Medical-Search', 'Web-Search', 'Finance-Search', 'Patent-Search', 'News-Search']:
                    search_prompt = build_search_prompt(body['user_prompt'], current_tool, []) # Don't involv pre_tool_uses
                    agent, agent_name = search_routing.get(tool_name, (MindSearchWebHitlAgent, "mindsearch"))
                    agent = agent()
                    step_body = {
                        "user_prompt": search_prompt,
                        "history_messages": [],
                        "agent": agent_name,
                        "skip_followup": True,
                        "params":{
                            "language": self.language,
                            "model": "",
                            "enable_rag": True,
                            "is_hitl": True,
                            }
                    }
                    generator = agent.start_wo_dump(**step_body)
                    ret['type'] = 'thought'
                elif self.database_rl and tool_name in ["Clinical-Trial-Result-Analysis", "Catalyst-Event-Analysis", "Drug-Analysis"]:
                    search_prompt = build_search_prompt(body['user_prompt'], current_tool, []) # Don't involv pre_tool_uses
                    agent, agent_name = search_routing.get(tool_name, (MindSearchWebHitlAgent, "mindsearch"))
                    agent = agent()
                    step_body = {
                        "user_prompt": search_prompt,
                        "history_messages": [],
                        "agent": agent_name,
                        "skip_followup": True,
                        "params":{
                            "language": self.language,
                            "model": "",
                            "enable_rag": True,
                            "is_hitl": True,
                            }
                    }
                    generator = agent.start_wo_dump(**step_body)
                    ret['type'] = 'thought'
                else:
                    context_data = await run_query(current_tool, self.output_dir, current_step, self.language, body['user_prompt'])
                    # Add import for CSV handling if not already at the top
                    step_body = await build_workflow_prompt(context_data, current_tool, self.language, self.output_dir, current_step, total_feedback, prev_tool_uses, plan)
                    agent = MindSearchWorkflowAgent()
                    generator = agent.start_wo_dump(**step_body)
                buffer = io.StringIO()
                chunk_idx = 0
                ret['current_tool'] = current_tool
                ret['chunkIdx'] = chunk_idx
                async for chunk in self._task_with_heartbeat(generator, interval=1, stream=stream):
                    try:
                        if await wait_for_interrupt_input(self, self.thread_id):
                            if ret['type'] == 'thought' and ret['message']:
                                try:
                                    thought = json.loads(ret['message'])
                                    thought['content'] = thought.get('content', '') + '\nInterrupted by user input.'
                                    ret['message'] = json.dumps(thought)
                                except:
                                    pass
                            else:
                                ret['message'] += '\nInterrupted by user input.'
                            for _ret in send_message_and_save(ret):
                                yield _ret
                            break
                        if not chunk:
                            continue
                        current_tool['result'] = chunk
                        if type(chunk) == dict:
                            content = chunk.get('content', '')
                        else:
                            content = chunk
                        if ret['type'] == 'thought' and type(chunk) == dict and content:
                            async for _ret in send_message_and_save(ret):
                                yield _ret
                            # chunk_idx += 1
                            # ret['chunkIdx'] = chunk_idx
                            ret['type'] = 'chat'
                        if ret['type'] == 'thought':
                            latest_content = json.dumps(chunk, ensure_ascii=False)
                        else:
                            latest_content = content
                        ret['message'] = latest_content
                        # ret['current_tool'] = current_tool
                        if ret.get('message', None):
                            yield ret
                    except Exception as e:
                        trace = traceback.format_exc()
                        logger.info(f"Error in chunk processing: {trace}")
                buffer.close()
                if self.stopped:
                    plan[-1]['status'] = 'error'
                    plan[-1]['result'] = ret.get('message', '')
                    ret['plan'] = plan
                    prev_tool_uses.append(plan[-1])
                    ret['tool_uses'] = prev_tool_uses
                    async for _ret in send_plan_update(ret):
                        yield _ret
                    return
                
                result_content = False
                if type(current_tool['result']) == dict and 'content' in current_tool['result']:
                    result_content = True
            
                citation_source = []
                if tool_name in ['Generate-Summary', 'General-Inference', 'Medical-Diagnosis']:
                    content = current_tool['result']['content'] if result_content else current_tool['result']
                    content, citation_source = format_content_citation(prev_tool_uses, content)
                    if result_content:
                        current_tool['result']['content'] = content
                    else:
                        current_tool['result'] = content
                
                # add reference event. check if there is source, this part is for mindsearch agent
                source = []
                if type(current_tool['result']) == dict:
                    source = current_tool['result'].get('search_graph', {}).get('source', [])
                # For summary node like Generate-Summary, we generate source from 
                if tool_name in ['Generate-Summary', 'General-Inference', 'Medical-Diagnosis']:
                    source = citation_source

                # Group citation in the download file
                citation_str = ""
                if len(source) > 0:
                    tmp_source = copy.deepcopy(source)
                    tmp_source.sort(key=lambda x: x['id'])
                    citation_str = ("\n").join([
                        f"- [{link['id']}]{link.get('title', '')}({link['url']})"
                        for link in tmp_source if 'id' in link and 'url' in link
                    ])

                # Get save content from result content of simple result
                save_content = current_tool['result']['content'] if result_content else current_tool['result']
                # Add reference in 
                if citation_str != "":
                    save_content += f"\n\n## Reference\n\n{citation_str}\n\n"
                save_to_file(save_content, self.output_dir, f"{current_step-1}_{current_tool['tool']}_NoahAI.md")
                
                if current_step == len(plan) and download_link:
                    try:
                        await self.upload_archive(f"{object_path}.zip", bucket_name, STORAGE_DEST)
                        ret['attachments_key'] = f"{self.output_dir}.zip"
                        if STORAGE_DEST == 'hw':
                            download_link = "\n\n" + ("## 下载链接：[结果与数据]" if self.language.lower() == 'cn' else "## Download link: [Results & Data]") + f"(https://{bucket_name}.obs.cn-south-1.myhuaweicloud.com/{encoded_object_path}.zip)"
                        else:
                            download_link = "\n\n" + ("## 下载链接：[结果与数据]" if self.language.lower() == 'cn' else "## Download link: [Results & Data]") + f"(https://noahdata.blob.core.windows.net/{bucket_name}/{encoded_object_path}.zip)"
                        
                        if result_content:
                            current_tool['result']['content'] += download_link
                        else:
                            current_tool['result'] += download_link
                    except:
                        trace = traceback.format_exc()
                        logger.info(f"Error in data upload: {trace}")
                # TODO: remove later
                current_tool['status'] = 'done'
                if isinstance(current_tool['result'], str):
                    full_result = current_tool['result']
                else:
                    full_result = str(current_tool['result'].get('content', ''))
                ret['message'] = full_result
                async for _ret in send_message_and_save(ret):
                    yield _ret
                
                # Send source event
                if len(source) > 0:
                    async for _ret in send_message_and_save(ret):
                        _ret['type'] = 'reference'
                        _ret['message'] = json.dumps(source, ensure_ascii=False)
                        chunk_idx += 1
                        _ret['chunkIdx'] = chunk_idx
                        yield _ret

            ret.pop('chunkIdx', '')
            ret.pop('message', '')
            
            ret['type'] = 'planUpdate'
            if prev_tool_uses and (prev_tool_uses[-1]['tool'] == 'Self-Reflection' or not prev_tool_uses[-1].get('result', '')):
                prev_tool_uses[-1] = current_tool.copy()
            else:
                prev_tool_uses.append(current_tool.copy())
            ret["tool_uses"] = prev_tool_uses
            ret.pop('attachments_key', '')
            # ret['current_tool'] = {}
            plan[current_step-1]['status'] = 'done'
            if len(plan) > current_step:
                plan[current_step]['status'] = 'doing'
                plan[current_step]['startedAt'] = int(time.time())
            ret['plan'] = plan
            async for _ret in send_message_and_save(ret):
                yield _ret
            current_step += 1
            ret['current_step'] = current_step
            if current_step-1 >= len(plan):
                should_run = False
                break
            if hitl_mode == 'always':
                should_run = False
            current_tool = plan[current_step-1].copy()
            if current_tool['tool'] not in ['Generate-Summary', 'Self-Reflection', 'User-Question', 'Plan-Sequence', 'General-Inference']:
                #TODO remove 'Drug-Analysis', 'Catalyst-Event-Analysis', 'Clinical-Trial-Result-Analysis'
                tool_filling_msg_json = await function_call_with_retry(self.tool_slot_filling, body, current_tool, data=prev_tool_uses, feedback=feedback)
                params_dict = {"original_params": tool_filling_msg_json.copy()}
                tool_filling_msg_json_cleaned = None
                if not self.database_rl:
                    if current_tool['tool'] in ["Clinical-Trial-Result-Analysis", "Catalyst-Event-Analysis"]:
                        tool_filling_msg_json_cleaned = clean_args_typesense(tool_filling_msg_json)
                    elif current_tool['tool'] == "Drug-Analysis":
                        tool_filling_msg_json_cleaned = clean_args_typesense(tool_filling_msg_json, citeline=True)
                        if not tool_filling_msg_json_cleaned.get('location', []):
                            tool_filling_msg_json_cleaned['location'] = ['USA', 'China', 'Japan', 'UK', 'France', 'Germany', 'Italy', 'Spain']
                params_dict["matched_params"] =  tool_filling_msg_json_cleaned or tool_filling_msg_json
                save_to_file(params_dict, self.output_dir+'_params', f"{current_step-1}_{current_tool['tool']}_params.json")
                current_tool['params'] = params_dict["matched_params"]
            ret['current_tool'] = current_tool
            
        if current_step-1 < len(plan):
            ret['type'] = 'statusUpdate'
            ret['agentStatus'] = 'waiting'
        else:
            # extra message to update plan
            ret['type'] = 'statusUpdate'
            ret['agentStatus'] = 'stopped'
            if 'taskStart' in planning_task:
                ret['taskStart'] = planning_task['taskStart']
            ret.pop('current_tool', None)
        async for _ret in send_message_and_save(ret):
            yield _ret
        print('z')
        # print("ret", ret)

    async def tool_slot_filling(self, body, current_tool, data=None, feedback=""):
        if self.database_rl:
            tool_map = tool_mapping_rl_cn if self.language.lower() == 'cn' else tool_mapping_rl
        else:
            tool_map = tool_mapping_cn if self.language.lower() == 'cn' else tool_mapping
        tool_name = current_tool['tool']
        tool_info = tool_map[tool_name]
        original_question_and_feedback = body['user_prompt']
        tool_info_prompt = tool_info.get('prompt', '')
        planning_format = get_openai_json_schema_v3(tool_info['schema'])
        previous_tool_result = ''
        if data:
            if type(data) == list:
                result = data[-1].get('result','')
                if type(result) == dict and 'content' in result and result['content']:
                    result = result['content']
            previous_tool_result = result
        current_tool_query_params = f"{query_params}" if (query_params:=current_tool.get('query_params','')) else ''
        current_tool_reason = ''
        original_question_prompt = ''
        if 'reason' in current_tool and current_tool['reason'] and current_tool['tool'] not in ['Drug-Analysis', 'Catalyst-Event-Analysis', 'Clinical-Trial-Result-Analysis']:
            # remove previous tool result
            previous_tool_result = ''
            current_tool_reason = f"The goal/reason for choosing this tool: {current_tool['reason']}"
            if not feedback: 
                original_question_prompt = f'(to answer the original user question: {original_question_and_feedback})'
        feedback_prompt = f'Consider the user feedback: {feedback}' if feedback else ''
        tool_slot_filling_prompt = tool_slot_filling_template.format(
            tool_info_prompt=tool_info_prompt,
            previous_tool_result=previous_tool_result,
            current_tool_query_params=current_tool_query_params,
            current_tool_reason=current_tool_reason,
            original_question_prompt=original_question_prompt,
            feedback_prompt=feedback_prompt,
        )
        function_name = planning_format[0]['function']['name']
        response = await self.slot_filling_llm(user_prompt=tool_slot_filling_prompt, tools=planning_format, tool_choice={"type": "function", "function": {"name": function_name}}, temperature=0.3, max_tokens=8192)
        return response
        
    async def extract_plan(self, planning_text, current_step = 0, planning_format = None):
        kwargs = {}
        if planning_format:
            function_name = planning_format[0]['function']['name']
            kwargs['tool_choice'] = {"type": "function", "function": {"name": function_name}}
            result = await function_call_with_retry(self.plan_extraction_llm, user_prompt=planning_text, tools=planning_format, planning=True, temperature=0.3, max_tokens=8192, **kwargs)
        sequence = result.get('planned_sequence', [])[:MAX_STEPS+1]
        if MAX_STEPS>1 and sequence and sequence[-1].get('tool', '') not in ['Self-Reflection', 'Generate-Summary', 'General-Inference', 'Medical-Diagnosis']:
            reflection_reason = '反思当前步骤的执行情况，判断是否需要调整计划并添加新的步骤。' if self.language == 'cn' else 'Reflect on the current step execution, whether to adjust the plan and add new steps.'
            sequence.append({'tool': 'Self-Reflection', 'status': 'todo', 'startedAt': int(time.time()), 'reason': reflection_reason})
        for step in sequence:
            step['status'] = 'todo' 
            step['startedAt'] = int(time.time())
        if sequence:
            sequence[0]['status'] = 'doing'
        return sequence
    
    async def _task_with_heartbeat(self, gen, interval: float = 0.3, stream=False):
        r"""
        Since fetch web page contents may cost very long time. Only yield when new data is available to avoid unnecessary returns.
        """
        buffer = io.StringIO()
        newest_chunk = None
        start_time = time.time()
        last_pos = 0  # 记录上次已返回的 buffer 位置
        # 记录上一次的hash值，兼容增量，全量返回
        newest_hash = hashlib.sha256("".encode()).hexdigest()  
        async def write_buffer():
            nonlocal newest_chunk
            async for chunk in gen:
                if not chunk:
                    continue
                if stream:
                    buffer.write(chunk)
                else:
                    if type(chunk) == str:
                        newest_chunk = chunk
                    elif type(chunk) == dict:
                        newest_chunk = chunk
        task = asyncio.create_task(write_buffer())
        # shielded = asyncio.shield(task)

        while not task.done():
            if self.stopped:
                task.cancel()
                logger.info("Task cancelled due to stop signal.")
                break
            if stream:
                current_value = buffer.getvalue()
                if len(current_value) > last_pos:
                    yield current_value
                    last_pos = len(current_value)
            elif newest_chunk:
                temp_newest_str = str(newest_chunk)
                temp_newest_hash = hashlib.sha256(temp_newest_str.encode()).hexdigest()
                if temp_newest_hash != newest_hash:
                    newest_hash = temp_newest_hash
                    yield newest_chunk
            await asyncio.sleep(interval)
        # await shielded
        await task
        end_time = time.time()
        if stream:
            current_value = buffer.getvalue()
            if len(current_value) > last_pos:
                yield current_value
        elif newest_chunk:
            temp_newest_str = str(newest_chunk)
            temp_newest_hash = hashlib.sha256(temp_newest_str.encode()).hexdigest()
            if temp_newest_hash != newest_hash:
                yield newest_chunk
        logger.info(f"[_task_with_heartbeat]{callable} cost time total {end_time - start_time}s")
        
    async def upload_archive(self, object_path, bucket_name, source='azure'):
        if source == 'hw':
            from utils.obs.client import upload_file
        else:
            from utils.azure.blob_client import upload_file
        # Save report and outputs to zip file
        zip_path = f"{self.output_dir}.zip"
        try:
            convert_md_to_docx(self.output_dir)
        except:
            pass
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir, exist_ok=True)
        shutil.make_archive(self.output_dir, 'zip', self.output_dir)
        logger.info(f"Output saved to {zip_path}")
        
        for _ in range(3):
            res = upload_file(bucket_name, object_path, zip_path)
            if res: 
                logger.info(f"File {zip_path} uploaded successfully")
                # Delete zip and original folder when upload is successful
                try:
                    os.remove(zip_path)  # Delete the zip file
                    shutil.rmtree(self.output_dir)  # Delete the original folder
                    logger.info(f"Cleaned up {zip_path} and {self.output_dir}")
                except Exception as e:
                    logger.error(f"Failed to clean up files: {str(e)}")
                break
            await asyncio.sleep(3)
        else:
            logger.error(f"Failed to upload {zip_path}")
        
    async def self_reflection(self, ret, user_question, prev_tool_uses, current_tool, current_step, plan, feedback, approve=None):
        for step in plan:
            step.pop('result', None)
            step.pop('params', None)
        if approve is not True or not prev_tool_uses or prev_tool_uses[-1]['tool'] != 'Self-Reflection':
            instructions = reflection_instructions_cn if self.language == 'cn' else reflection_instructions
            reflection_instructions_prompt = instructions.format(additional_step_count=min(4,MAX_STEPS_WITH_REFLECTION-(len(plan)-current_step)))
            
            if prev_tool_uses[-1]['tool'] == 'Self-Reflection':
                prior_knowledge = tool_history_to_prompt(prev_tool_uses[-1:], is_plan=True)
            else:
                prior_knowledge = tool_history_to_prompt(prev_tool_uses)
            
            
            reflection_prompt = reflection_template.format(
                current_date=datetime.now().strftime('%B %d, %Y'), 
                instructions_prompt=reflection_instructions_prompt,
                user_prompt=user_question,
                current_plan=plan,
                prior_knowledge=prior_knowledge,
                user_feedback=feedback,
            )
            
            ret['type'] = 'chat'
            response_stream = self.plan_llm.stream_call(user_prompt=reflection_prompt)
            async for chunk in task_with_heartbeat(response_stream, interval=1, stream=True):
                if not chunk: continue
                ret['message'] = chunk
                yield ret

            tool_use = ret['current_tool'].copy()
            tool_use['result'] = ret['message']
            if prev_tool_uses: 
                if prev_tool_uses[-1]['tool'] != 'Self-Reflection':
                    prev_tool_uses.append(tool_use)
                else:
                    prev_tool_uses[-1]['result'] = ret['message']
            else:
                prev_tool_uses.append(tool_use)
            ret['tool_uses'] = prev_tool_uses

            async for _ret in send_message_and_save(ret):
                yield _ret
                
            ret.pop('message', None)
            ret.pop('chunkIdx', '')

            countdown_seconds = 20
            async for _ret in send_agent_status_update(ret, 'waiting', countdown_seconds=countdown_seconds):
                yield _ret
            if (await wait_for_confirm_tool_input(self, seconds=countdown_seconds+1)) is False:
                return
            async for _ret in send_confirm_tool(ret, None, True):
                yield _ret
            async for _ret in send_agent_status_update(ret, 'running'):
                yield _ret
        current_tool['result'] = reflection_message = chunk if approve is not True else prev_tool_uses[-1]['result']
        save_to_file(current_tool['result'], self.output_dir, f"{current_step-1}_{current_tool['tool']}_NoahAI.md")
        
        reflection_extraction_template = reflection_extraction_template_en if self.language == 'en' else reflection_extraction_template_cn
        reflection_extraction_prompt = reflection_extraction_template.format(reflection=reflection_message)
        
        reflection_schema = get_openai_json_schema_v3(ReflectionSchema)
        tool_choice = {"type": "function", "function": {"name": reflection_schema[0]['function']['name']}}
        slot_fill_result = await function_call_with_retry(self.slot_filling_llm, user_prompt=reflection_extraction_prompt, tools=reflection_schema, tool_choice=tool_choice, planning=True, temperature=0.3, max_tokens=8192)

        additional_steps = slot_fill_result.get('additional_steps', [])[:min(4,MAX_STEPS_WITH_REFLECTION+1-(len(plan)-current_step))]
        if not additional_steps:
            summary_tool = {'tool': 'Generate-Summary', 'status': 'todo', 'startedAt': int(time.time())}
            summary_tool['reason'] = "总结上述步骤的结果并回答用户的问题" if self.language == 'cn' else "Summarize the results of the previous steps and answer the user's question"
            additional_steps = [summary_tool]
        elif additional_steps[-1].get('tool', '') not in ['Generate-Summary', 'Self-Reflection', 'General-Inference', 'Medical-Diagnosis']:
            reflection_reason = '反思当前步骤的执行情况，判断是否需要调整计划并添加新的步骤。' if self.language == 'cn' else 'Reflect on the current step execution, whether to adjust the plan and add new steps.'
            reflection_tool = {'tool': 'Self-Reflection', 'status': 'todo', 'startedAt': int(time.time()), 'reason': reflection_reason}
            additional_steps.append(reflection_tool)
        for step in additional_steps:
            step['status'] = 'todo' 
            step['startedAt'] = int(time.time())
            
        plan = plan[:current_step] + additional_steps
        ret['plan'] = plan
        current_tool['status'] = 'done'