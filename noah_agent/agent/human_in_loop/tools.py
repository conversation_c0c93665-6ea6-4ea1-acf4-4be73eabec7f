import asyncio
from datetime import datetime
import io
import os
import re
import shutil
import time
import traceback
from typing import Callable, List, Type

from fastapi.responses import StreamingResponse
from agent.human_in_loop.query import run_query
from agent.human_in_loop.constants import background_prefix_map
from agent.explore.mindsearch_workflow_agent import MindSearchWorkflowAgent
from agent.explore.mindsearch_agent import MindSearchAgent, MindSearchOfficialAgent
from llm.gcp_models import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>37<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>37ThinkingBypass, Gemini25<PERSON><PERSON>
from agent.human_in_loop.planning_v2 import PlanningAgent
from utils.human_in_loop.helpers import *
from utils.core.get_json_schema import get_openai_json_schema, get_openai_json_schema_v2, get_openai_json_schema_v3
from utils.human_in_loop.logprob import process_prob
from tools.human_in_loop.planning.schema import *
from tools.human_in_loop.planning.schema_cn import *
from tools.human_in_loop.planning.prompt import *

from agent.core.preset import AgentPreset
from llm.azure_models import GPT4o, Ada
from llm.base_model import BaseLLM
from llm.deepseek_models import ClaudeThenDeepseekChat, CompositeDeepseekChat, CompositeDeepseekReasoner
from utils.clinical_utils.clean_args_typesense import clean_args_typesense
import json
import logging

tool_mapping = {
    # "NCCN-Guidelines": {"schema": NCCNGuidelinesInputSchema, "prompt": nccn_slot_filling_prompt},
    "General-Inference": {"schema": GeneralInferenceInputSchema, "prompt": general_inference_slot_filling_prompt},
    "Medical-Search": {"schema": MedicalSearchInputSchema, "prompt": medical_search_slot_filling_prompt},
    "Web-Search": {"schema": WebSearchInputSchema, "prompt": web_search_slot_filling_prompt},
    "Clinical-Trial-Result-Analysis": {"schema": ClinicalResultsInputSchema, "prompt": clinical_trial_results_slot_filling_prompt},
    "Drug-Analysis": {"schema": DrugCompetitionLandscapeInputSchema, "prompt": drug_competition_landscape_slot_filling_prompt},
    "Catalyst-Event-Analysis": {"schema": CatalystSearchInputSchema, "prompt": catalyst_search_slot_filling_prompt},
    # "Summarize-Results": {"schema": SummarizeResultsInputSchema, "prompt": summarize_results_prompt},
}

tool_mapping_cn = {
    # "NCCN-Guidelines": {"schema": NCCNGuidelinesInputSchema, "prompt": nccn_slot_filling_prompt},
    "General-Inference": {"schema": GeneralInferenceInputSchemaCn, "prompt": general_inference_slot_filling_prompt},
    "Medical-Search": {"schema": MedicalSearchInputSchemaCn, "prompt": medical_search_slot_filling_prompt},
    "Web-Search": {"schema": WebSearchInputSchemaCn, "prompt": web_search_slot_filling_prompt},
    "Clinical-Trial-Result-Analysis": {"schema": ClinicalResultsInputSchemaCn, "prompt": clinical_trial_results_slot_filling_prompt},
    "Drug-Analysis": {"schema": DrugCompetitionLandscapeInputSchemaCn, "prompt": drug_competition_landscape_slot_filling_prompt},
    "Catalyst-Event-Analysis": {"schema": CatalystSearchInputSchemaCn, "prompt": catalyst_search_slot_filling_prompt},
    # "Summarize-Results": {"schema": SummarizeResultsInputSchema, "prompt": summarize_results_prompt},
}
llm = CompositeDeepseekChat()


    
class PlanningTools():
    def __init__(self, **kwargs):
        self.language = kwargs.get('language', 'en')
        self.tool = kwargs.get('tool', None)
        self.slot_fill_mode = kwargs.get('slot_fill', False)
        self.user_prompt = kwargs.get('user_prompt', None)
        if not self.tool:
            raise ValueError("Tool is required.")
        if not self.user_prompt:
            raise ValueError("User prompt is required.")
        self.tool_map = tool_mapping_cn if self.language.lower() == 'cn' else tool_mapping
        self.output_dir = f"outputs/planning/jiaoda/{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir, exist_ok=True)
            
    async def tool_slot_filling(self, body, current_tool):
        tool_map = tool_mapping_cn if self.language.lower() == 'cn' else tool_mapping
        tool_name = self.tool
        tool_info = tool_map[tool_name]
        body = body.copy()
        if tool_info['prompt']:
            body['user_prompt'] = tool_info['prompt'] + 'You must respond via function call.'
        body['user_prompt'] += '\nThe user question ' + self.user_prompt
        planning_format = get_openai_json_schema_v3(tool_info['schema'])
        function_name = planning_format[0]['function']['name']
        response = await llm(**body, tools=planning_format, tool_choice={"type": "function", "function": {"name": function_name}})
        return response

    async def slot_fill(self):
        if self.tool not in self.tool_map:
            raise ValueError(f"Tool {self.tool} is not supported. Choose from {list(self.tool_map.keys())}.")
        body = {"user_prompt": self.user_prompt}
        current_tool = {
            "tool": self.tool,
        }
        tool_filling_msg_json = await function_call_with_retry(self.tool_slot_filling, body, current_tool)
        return tool_filling_msg_json
        
    async def run_tool(self):
        if self.tool not in self.tool_map:
            raise ValueError(f"Tool {self.tool} is not supported. Choose from {list(self.tool_map.keys())}.")
        body = {"user_prompt": self.user_prompt}
        current_tool = {
            "tool": self.tool,
        }
        tool_filling_msg_json = await function_call_with_retry(self.tool_slot_filling, body, current_tool)
        if self.tool in ["Clinical-Trial-Result-Analysis", "Catalyst-Event-Analysis"]:
            tool_filling_msg_json = clean_args_typesense(tool_filling_msg_json)
        elif self.tool == "Drug-Analysis":
            tool_filling_msg_json = clean_args_typesense(tool_filling_msg_json, citeline=True)
            if not tool_filling_msg_json.get('location', []):
                tool_filling_msg_json['location'] = ['USA', 'China', 'Japan', 'UK', 'France', 'Germany', 'Italy', 'Spain']
        current_tool['params'] = {k: v for k, v in tool_filling_msg_json.items() if v}
        context_data = await run_query(current_tool, self.output_dir, 1, language=self.language, user_prompt=self.user_prompt)
        background_prefix = background_prefix_map[self.tool] if self.tool in background_prefix_map else ''
        extra_explanation = ""
        if self.tool == "Drug-Analysis" and current_tool['params'].get('location', []):
            extra_explanation = f"(Please note that the location is limited to {current_tool['params']['location']})"
        stream = False
        if self.tool == 'General-Inference':
            inference_prompt = self.build_inference_prompt(body['user_prompt'], current_tool, [])
            generator = ClaudeThenDeepseekChat().stream_call(user_prompt=inference_prompt)
            stream = True
        elif self.tool in ['Medical-Search', 'Web-Search']:
            search_prompt = build_search_prompt(body['user_prompt'], current_tool, [])
            step_body = {
                "user_prompt": search_prompt,
                "history_messages": [],
                "agent": "mindsearchofficialsite" if self.tool == "Medical-Search" else "mindsearch",
                "skip_followup": True,
                "params":{
                    "language": self.language,
                    "model": "",
                    "enable_rag": True,
                    }
            }
            agent = MindSearchOfficialAgent() if self.tool == "Medical-Search" else MindSearchAgent()
            generator = agent.start_wo_dump(**step_body)
        else:
            step_body = {
                "user_prompt": (current_tool.get('params', None) or {}).get('question','') + " (Do not make assumptions on data, only use whatever context/data has been provided to you.)",
                "history_messages": [],
                "agent":"mindsearchworkflowrefer",
                "skip_followup": True,
                "params":{
                    "language": self.language,
                    "model": "",
                    "enable_rag": False,
                    "background": f"{background_prefix}{extra_explanation} {context_data}"
                    }
            }
            agent = MindSearchWorkflowAgent()
            generator = agent.start_wo_dump(**step_body)
        if stream: 
            buffer = io.StringIO()
            async for chunk in generator:
                if not chunk:
                    continue
                buffer.write(chunk)
                yield buffer.getvalue()
        else:
            async for chunk in generator:
                if not chunk:
                    continue
                if type(chunk) == dict:
                    newest_chunk = chunk.get('content', '')
                else:
                    newest_chunk = chunk
                yield newest_chunk
    
    def run_agent(self):
        params = {
            'user_prompt': self.user_prompt, 
            'params': {'language': 'CN', 
                       'model': 'deepseek-r1', 
                       'enable_rag': 'false', 
                       'background': ''}, 
            'thread_id': 'xxxxxxxxx-xxxx-xxxx-xxxxxxxxxxxx', 
            'question': self.user_prompt, 
            'enable_rag': 'false', 
            'model': 'deepseek-r1', 
            'language': self.language, 
            'mock': True, 
            'type': 'chat', 
            'approve': True, 
            'hitl_mode': 'never',
            'download_link': False,
            'planning_task': {'id': 'xxxxxxxxx-xxxx-xxxx-xxxxxxxxxxxx', 
                              'user': '<EMAIL>'}
            }
        agent = PlanningAgent(**params)
        return agent.start_wo_dump(**params)
    
    async def run(self):
        if self.tool == 'agent':
            latest_ret = ''
            async for ret in self.run_agent():
                if not ret:
                    continue
                latest_ret = ret
            try:
                return latest_ret['tool_uses'][-1]['result'] 
            except:
                return latest_ret.get('tool_uses', latest_ret)
        elif self.slot_fill_mode:
            return await self.slot_fill()  
        else:
            latest_ret = ''
            async for ret in self.run_tool():
                if not ret:
                    continue
                latest_ret = ret
            if type(latest_ret) == dict:
                return latest_ret.get('current_tool', latest_ret)
            return latest_ret