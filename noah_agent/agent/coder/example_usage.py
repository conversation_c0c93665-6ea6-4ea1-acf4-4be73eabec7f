"""
Coder智能体使用示例
展示如何使用可插拔的Coder功能
"""
import asyncio
from agent.coder.simple_coder_agent import SimpleCoderAgent


async def example_full_features():
    """
    示例1：使用完整功能的Coder智能体
    包含代码生成、执行、文件管理和审查
    """
    print("=== 完整功能Coder智能体示例 ===")
    
    # 创建完整功能的Coder智能体
    coder = SimpleCoderAgent(
        work_dir="./example_workspace",
        enable_file_management=True,  # 启用文件管理
        enable_code_review=True       # 启用代码审查
    )
    
    # 使用智能体
    user_request = "请帮我创建一个简单的计算器类，包含加减乘除功能，然后测试它并保存到文件"
    
    async for response in coder.use_tool(
        user_prompt=user_request,
        language="python",
        ui_language="cn"
    ):
        # 打印响应信息
        if hasattr(response, 'search_graph') and response.search_graph:
            print(f"当前操作: {response.search_graph.query}")
            
            # 显示子操作
            for child in response.search_graph.children:
                status = "✅" if child.processing_type == 3 else "🔄"  # 3 = DONE
                print(f"  {status} {child.query}: {child.summary}")
        
        if hasattr(response, 'content') and response.content:
            print(f"AI回复: {response.content}")


async def example_minimal_features():
    """
    示例2：使用最小功能的Coder智能体
    只包含代码生成和执行，不包含文件管理和审查
    """
    print("\n=== 最小功能Coder智能体示例 ===")
    
    # 创建最小功能的Coder智能体
    coder = SimpleCoderAgent(
        work_dir="./minimal_workspace",
        enable_file_management=False,  # 禁用文件管理
        enable_code_review=False       # 禁用代码审查
    )
    
    # 使用智能体
    user_request = "生成一个Python函数来计算斐波那契数列，然后执行它"
    
    async for response in coder.use_tool(
        user_prompt=user_request,
        language="python",
        ui_language="en"
    ):
        # 简单打印响应
        if hasattr(response, 'processing_type') and response.processing_type == 7:  # RESPONSEDONE
            print("任务完成!")


async def example_custom_workspace():
    """
    示例3：自定义工作空间的Coder智能体
    """
    print("\n=== 自定义工作空间Coder智能体示例 ===")
    
    # 创建自定义工作空间的Coder智能体
    coder = SimpleCoderAgent(
        work_dir="/tmp/my_custom_coder_workspace",  # 自定义工作目录
        enable_file_management=True,
        enable_code_review=True
    )
    
    # 使用智能体进行文件操作
    user_request = "创建一个Python模块，包含数据处理的工具函数，并审查代码质量"
    
    async for response in coder.use_tool(
        user_prompt=user_request,
        language="python",
        ui_language="cn"
    ):
        # 处理响应...
        pass


def demonstrate_pluggable_design():
    """
    演示可插拔设计的优势
    """
    print("\n=== 可插拔设计演示 ===")
    
    # 场景1：只需要代码生成和执行
    basic_coder = SimpleCoderAgent(
        enable_file_management=False,
        enable_code_review=False
    )
    print("✅ 基础Coder创建成功 - 只有代码生成和执行功能")
    
    # 场景2：需要完整功能
    full_coder = SimpleCoderAgent(
        enable_file_management=True,
        enable_code_review=True
    )
    print("✅ 完整Coder创建成功 - 包含所有功能")
    
    # 场景3：只需要代码生成和文件管理
    file_coder = SimpleCoderAgent(
        enable_file_management=True,
        enable_code_review=False
    )
    print("✅ 文件Coder创建成功 - 代码生成 + 文件管理")
    
    # 显示工具配置
    print(f"\n基础Coder工具数量: {len(basic_coder.tools)}")
    print(f"完整Coder工具数量: {len(full_coder.tools)}")
    print(f"文件Coder工具数量: {len(file_coder.tools)}")


async def example_search_node_tracking():
    """
    示例4：演示search_node的实时跟踪功能
    """
    print("\n=== Search Node实时跟踪示例 ===")
    
    coder = SimpleCoderAgent(
        work_dir="./tracking_workspace",
        enable_file_management=True,
        enable_code_review=True
    )
    
    user_request = "创建一个简单的待办事项管理器"
    
    async for response in coder.use_tool(
        user_prompt=user_request,
        language="python",
        ui_language="cn"
    ):
        # 详细跟踪search_node状态
        if hasattr(response, 'search_graph') and response.search_graph:
            root = response.search_graph
            print(f"\n📋 根节点: {root.query}")
            print(f"   状态: {root.processing_type}")
            print(f"   思考: {root.thought_process}")
            
            # 遍历子节点
            for i, child in enumerate(root.children):
                status_map = {
                    0: "❓ 未知",
                    1: "🔄 处理中", 
                    2: "💬 响应中",
                    3: "✅ 完成",
                    5: "💭 思考中",
                    9: "🤔 分析中"
                }
                status = status_map.get(child.processing_type, "❓")
                
                print(f"   └─ 步骤{i+1}: {status} {child.query}")
                if child.summary:
                    print(f"      摘要: {child.summary}")
                if child.key_word:
                    print(f"      关键词: {child.key_word}")


async def main():
    """主函数 - 运行所有示例"""
    print("🚀 Coder智能体示例程序启动")
    
    # 演示可插拔设计
    demonstrate_pluggable_design()
    
    # 运行异步示例
    try:
        # await example_full_features()
        # await example_minimal_features() 
        # await example_custom_workspace()
        await example_search_node_tracking()
        
    except Exception as e:
        print(f"❌ 示例运行出错: {e}")
    
    print("\n🎉 所有示例运行完成")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())


# ================================
# 重要说明和最佳实践
# ================================

"""
🔧 可插拔设计的优势：

1. 灵活配置：
   - 可以根据需求选择性启用功能
   - 减少不必要的工具加载
   - 提高性能和安全性

2. 安全隔离：
   - 所有文件操作限制在工作目录内
   - 代码执行在安全环境中进行
   - 防止系统文件被误操作

3. 实时反馈：
   - search_node提供详细的操作进度
   - 用户可以实时看到AI的工作状态
   - 便于调试和监控

4. 易于扩展：
   - 新工具可以轻松添加
   - 现有工具可以独立修改
   - 支持自定义工具配置

📝 使用建议：

1. 根据实际需求选择功能：
   - 简单任务：只启用代码生成和执行
   - 项目开发：启用所有功能
   - 代码审查：重点启用审查功能

2. 设置合适的工作目录：
   - 使用专门的工作目录
   - 定期清理临时文件
   - 备份重要代码

3. 监控执行状态：
   - 关注search_node的状态变化
   - 及时处理错误和异常
   - 记录重要操作日志

4. 安全注意事项：
   - 不要在生产环境中执行未知代码
   - 定期审查生成的代码
   - 限制执行权限和资源使用
"""
