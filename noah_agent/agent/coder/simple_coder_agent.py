"""
简单的Coder智能体 - 可插拔设计
包含代码生成、执行、文件管理和审查功能
"""
import time
from typing import List, Dict, Any, Optional

from pydantic import Field

from agent.core.preset import AgentPreset
from agent.explore.schema import (
    CodeResponse, CodeSubject, MindSearchResponse, SearchNode, SearchType, 
    ProcessingType, CodeNode, CodeType
)
from agent.explore.helper import MindSearchHelper
from llm.azure_models import GPT4o
from llm.base_model import BaseLLM
from utils.core.standardize import standardize_yield_wo_dump
from tools.coder.simple_code_tools import (
    CodeGeneration, CodeExecution, FileManagement, 
    CodeReview, Finished
)


class SimpleCoderAgent(AgentPreset):
    """
    简单的Coder智能体
    重要特性：
    1. 可插拔工具设计 - 可以选择性启用功能
    2. 安全的工作环境 - 限制在指定目录内操作
    3. 实时进度跟踪 - 通过search_node显示状态
    4. 智能代码处理 - 结合LLM和静态分析
    """
    
    # 基础配置
    llm: BaseLLM = GPT4o
    # TODO 放进prompt文件中
    sys_prompt: str = """
        你是一个专业的代码助手，可以帮助用户：
        1. 生成高质量的代码
        2. 安全地执行代码
        3. 管理项目文件
        4. 审查代码质量

        请始终遵循最佳实践，确保代码安全可靠。
        只要生成了代码，就必须执行，结果必须print出来。
        只要用到了CodeGeneration工具，紧接着就必须要用CodeExecution工具执行。

        user_prompt:
        {user_prompt}
    """
    
    # 工具配置 - 重要：可插拔设计，可以选择性启用功能
    # tools: List[BaseTool] = [
    #     CodeGeneration,    # 代码生成（核心功能）
    #     CodeExecution,     # 代码执行（核心功能）
    #     FileManagement,    # 文件管理（可选功能）
    #     CodeReview,        # 代码审查（可选功能）
    #     Finished          # 完成标记
    # ]
    tool_choice: str = "auto"
    
    # 新增字段声明 - 重要：必须在类级别声明所有字段
    work_dir: str = Field(default="./coder_workspace", description="工作目录路径")
    sandbox_url: str = Field(default="http://0.0.0.0:8194", description="沙箱服务地址")
    use_sandbox: bool = Field(default=True, description="是否使用沙箱模式")
    enable_file_management: bool = Field(default=True, description="是否启用文件管理功能")
    enable_code_review: bool = Field(default=True, description="是否启用代码审查功能")
    tool_config: dict = Field(default_factory=dict, description="工具配置字典")
    
    # 辅助组件
    helper: MindSearchHelper = Field(default_factory=MindSearchHelper)
    
    def __init__(self, work_dir: str = "./coder_workspace", enable_file_management: bool = True,
                 enable_code_review: bool = True, sandbox_url: str = "http://0.0.0.0:8194",
                 use_sandbox: bool = True, **kwargs):
        """
        初始化Coder智能体

        Args:
            work_dir: 本地工作目录路径（沙箱模式下作为备选）
            enable_file_management: 是否启用文件管理功能
            enable_code_review: 是否启用代码审查功能
            sandbox_url: 沙箱服务地址
            use_sandbox: 是否使用沙箱模式
        """
        # 重要：必须先调用父类初始化，传递所有参数
        super().__init__(
            work_dir=work_dir,
            enable_file_management=enable_file_management,
            enable_code_review=enable_code_review,
            sandbox_url=sandbox_url,
            use_sandbox=use_sandbox,
            tool_config={
                'work_dir': work_dir,
                'sandbox_url': sandbox_url,
                'use_sandbox': use_sandbox
            },
            **kwargs
        )

        # 重要：可插拔功能配置
        # 根据参数动态配置工具列表
        self.tools = [CodeGeneration, CodeExecution, Finished]  # 核心工具

        # 文件系统
        if self.enable_file_management:
            self.tools.insert(-1, FileManagement)

        # 代码审查
        if self.enable_code_review:
            self.tools.insert(-1, CodeReview)

        # 为工具传递沙箱配置
        self.tool_config = {
            'work_dir': self.work_dir,
            'sandbox_url': self.sandbox_url,
            'use_sandbox': self.use_sandbox
        }

    @standardize_yield_wo_dump
    async def start_wo_dump(self, *args, **kwargs):
        async for chunk in self.use_tool(*args, **kwargs):
            yield chunk
    
    async def use_tool(self, user_prompt: str, history_messages: List[dict] = [], **kwargs):
        """
        重写use_tool方法，实现Coder特定的逻辑和search_node管理
        
        重要流程：
        1. 初始化响应和search_graph
        2. 添加思考节点显示分析过程
        3. 执行工具调用并实时更新节点状态
        4. 生成会话总结
        """
        start_time = time.time()
        
        # 1. 初始化代码会话上下文
        code_context = {
            'work_dir': self.work_dir,
            'sandbox_url': self.sandbox_url,
            'use_sandbox': self.use_sandbox,
            'language': kwargs.get('language', 'python'),
            'operations': [],  # 记录所有操作
            'files_created': [],
            'code_generated': [],
            'executions': [],
            'reviews': []
        }
        
        language = kwargs.get('ui_language', 'en')  # 界面语言
        
        # 2. 初始化响应和search_graph（重要：为前端提供实时状态）
        response = self._init_coder_response(user_prompt, language)
        yield response
        
        # 3. 添加思考节点（重要：让用户看到AI的思考过程）
        self._add_thinking_node(response, user_prompt, language)
        yield response
        
        # 4. 执行父类的工具调用逻辑
        current_node = None
        
        # 过滤kwargs，只保留LLM支持的参数，避免work_dir等参数传递到LLM层
        allowed_keys = {'language', 'ui_language', 'images', 'temperature', 'max_tokens', 'json_mode', 'history_messages'}
        filtered_kwargs = {k: v for k, v in kwargs.items() if k in allowed_keys}

        prompt = self.sys_prompt.format(user_prompt=user_prompt)

        async for chunk in super().use_tool(prompt, history_messages, **filtered_kwargs):
            
            # 5. 处理LLM的工具调用响应
            if hasattr(chunk, 'tool_calls') and chunk.tool_calls:
                for tool_call in chunk.tool_calls:
                    # 为每个工具调用创建专门的节点（重要：实时显示进度）
                    current_node = self._add_tool_node(response, tool_call.function.name, language)
                    yield response
                    
                    # 更新节点的初始信息
                    self._update_node_from_tool_call(current_node, tool_call, language)
            
            # 6. 处理工具执行结果
            elif isinstance(chunk, dict) and 'function' in chunk:

                # 记录操作到上下文
                await self._record_operation(chunk, code_context)
                
                # 更新当前节点的完成状态（重要：显示操作结果）
                # 根据function名称找到对应的工具节点
                function_name = chunk.get('function', '')
                if response.code_graph and response.code_graph.children:
                    for child in response.code_graph.children:
                        if hasattr(child, 'query') and function_name in child.query:
                            child.processing_type = ProcessingType.DONE
                            child.summary = self._format_operation_summary(chunk, language)
                            break

                yield chunk


            # 7. 处理其他响应（如LLM的文本输出）
            else:
                print(f"🔍 DEBUG: 其他类型响应: {chunk}")
                yield chunk
        
        # 8. 生成会话总结节点（重要：为用户提供完整的操作总结）
        self._add_summary_node(response, code_context, language)
        yield response
        
        # 9. 标记响应完成
        response.processing_type = ProcessingType.RESPONSEDONE
        yield response
        
        print(f"Coder会话完成，耗时: {time.time() - start_time:.2f}秒")
    
    def _init_coder_response(self, user_prompt: str, language: str) -> CodeResponse:
        """
        初始化Coder响应对象
        重要：创建search_graph的根节点
        """
        def get_thought_process(lang: str) -> str:
            thoughts = {
                'cn': '代码助手正在分析您的需求...',
                'en': 'Code assistant is analyzing your requirements...',
                'jp': 'コードアシスタントが要件を分析中...'
            }
            return thoughts.get(lang, thoughts['en'])
        
        # 创建响应对象，包含search_graph根节点
        response = CodeResponse(
            processing_type=ProcessingType.PROCESSING,
            code_graph=CodeNode(
                code_type=CodeType.UNKNOWN,
                query=user_prompt,
                thought_process=get_thought_process(language),
                subject=CodeSubject.CODE,  # 标记为代码相关主题
                processing_type=ProcessingType.PROCESSING
            )
        )
        
        return response
    
    def _add_thinking_node(self, response: MindSearchResponse, query: str, language: str) -> SearchNode:
        """
        添加思考节点
        重要：让用户看到AI的分析过程
        """
        def get_thinking_text(lang: str) -> str:
            texts = {
                'cn': '分析代码需求和最佳实现方案...',
                'en': 'Analyzing code requirements and optimal implementation...',
                'jp': 'コード要件と最適な実装を分析中...'
            }
            return texts.get(lang, texts['en'])
        
        # 检查是否已有思考节点
        if response.code_graph.children:
            last_node = response.code_graph.children[-1]
            if last_node.processing_type == ProcessingType.THINKING:
                return last_node
        
        # 创建新的思考节点
        thinking_node = CodeNode(
            search_type=CodeType.UNKNOWN,
            query=get_thinking_text(language),
            processing_type=ProcessingType.THINKING
        )
        
        response.code_graph.add_child(thinking_node)
        return thinking_node
    
    def _add_tool_node(self, response: MindSearchResponse, tool_name: str, language: str) -> SearchNode:
        """
        为工具调用添加专门的节点
        重要：为每个操作创建可视化节点
        """
        # 工具名称本地化
        tool_names = {
            'CodeGeneration': {'cn': '代码生成', 'en': 'Code Generation', 'jp': 'コード生成'},
            'CodeExecution': {'cn': '代码执行', 'en': 'Code Execution', 'jp': 'コード実行'},
            'FileManagement': {'cn': '文件管理', 'en': 'File Management', 'jp': 'ファイル管理'},
            'CodeReview': {'cn': '代码审查', 'en': 'Code Review', 'jp': 'コードレビュー'},
            'Finished': {'cn': '完成', 'en': 'Finished', 'jp': '完了'}
        }
        
        display_name = tool_names.get(tool_name, {}).get(language, tool_name)
        
        # 根据工具类型设置不同的search_type
        code_type_map = {
            'CodeGeneration': CodeType.CodeGeneration,
            'CodeExecution': CodeType.CodeExecution,
            'FileManagement': CodeType.FileManagement,
            'CodeReview': CodeType.CodeReview,
            'Finished': CodeType.Finished
        }
        
        tool_node = CodeNode(
            code_type=code_type_map.get(tool_name, SearchType.WEB),
            query=display_name,
            processing_type=ProcessingType.PROCESSING
        )
        
        response.code_graph.add_child(tool_node)
        return tool_node
    
    def _update_node_from_tool_call(self, node: SearchNode, tool_call, language: str):
        """
        从工具调用更新节点信息
        重要：显示工具调用的参数信息
        """
        try:
            import json
            args = json.loads(tool_call.function.arguments)
            
            # 根据不同工具显示关键参数
            tool_name = tool_call.function.name
            if tool_name == 'CodeGeneration':
                desc = args.get('task_description', '')
                node.key_word = desc[:50] + "..." if len(desc) > 50 else desc
            elif tool_name == 'CodeExecution':
                lang = args.get('language', 'python')
                node.key_word = f"{lang} 代码执行"
            elif tool_name == 'FileManagement':
                op = args.get('operation', '')
                path = args.get('file_path', '')
                node.key_word = f"{op}: {path}"
            elif tool_name == 'CodeReview':
                review_type = args.get('review_type', 'basic')
                node.key_word = f"{review_type} 审查"
            
            # 添加思考过程（如果有）
            if 'thought_process' in args:
                node.summary = args['thought_process']
                
        except Exception as e:
            print(f"更新节点信息失败: {e}")
    
    async def _record_operation(self, result: dict, context: dict):
        """
        记录操作到上下文
        重要：跟踪所有操作历史
        """
        function_name = result.get('function', '')
        context['operations'].append({
            'function': function_name,
            'timestamp': time.time(),
            'result': result
        })
        
        # 按类型分类记录
        if function_name == 'CodeGeneration':
            context['code_generated'].append(result)
        elif function_name == 'CodeExecution':
            context['executions'].append(result)
        elif function_name == 'FileManagement':
            if result.get('params', {}).get('operation') in ['create', 'write']:
                context['files_created'].append(result)
        elif function_name == 'CodeReview':
            context['reviews'].append(result)
    
    def _format_operation_summary(self, result: dict, language: str) -> str:
        """
        格式化操作结果摘要
        重要：为用户提供清晰的操作反馈
        """
        function_name = result.get('function', '')
        
        if function_name == 'CodeGeneration':
            code_length = result.get('code_length', 0)
            lang = result.get('language', 'python')
            if language == 'cn':
                return f"生成了 {code_length} 字符的 {lang} 代码"
            else:
                return f"Generated {code_length} characters of {lang} code"
        
        elif function_name == 'CodeExecution':
            exec_result = result.get('execution_result', {})
            status = exec_result.get('status', 'unknown')
            if language == 'cn':
                return f"代码执行{status}，状态: {status}"
            else:
                return f"Code execution {status}"
        
        elif function_name == 'FileManagement':
            operation = result.get('params', {}).get('operation', '')
            if language == 'cn':
                return f"文件{operation}操作完成"
            else:
                return f"File {operation} operation completed"
        
        elif function_name == 'CodeReview':
            review_result = result.get('review_result', {})
            issues = review_result.get('issues_count', 0)
            if language == 'cn':
                return f"代码审查完成，发现 {issues} 个问题"
            else:
                return f"Code review completed, found {issues} issues"
        
        return "操作完成" if language == 'cn' else "Operation completed"
    
    def _add_summary_node(self, response: MindSearchResponse, context: dict, language: str) -> SearchNode:
        """
        添加会话总结节点
        重要：为用户提供完整的操作总结
        """
        def get_summary_query(lang: str) -> str:
            queries = {
                'cn': '生成代码会话总结...',
                'en': 'Generating code session summary...',
                'jp': 'コードセッションサマリーを生成中...'
            }
            return queries.get(lang, queries['en'])
        
        # 生成总结内容
        summary_content = self._generate_session_summary(context, language)
        
        summary_node = CodeNode(
            search_type=CodeType.ASSISTANT,
            query=get_summary_query(language),
            summary=summary_content,
            processing_type=ProcessingType.DONE
        )
        
        response.code_graph.add_child(summary_node)
        return summary_node
    
    def _generate_session_summary(self, context: dict, language: str) -> str:
        """
        生成会话总结内容
        重要：提供有价值的操作统计
        """
        total_ops = len(context['operations'])
        code_generated = len(context['code_generated'])
        executions = len(context['executions'])
        files_created = len(context['files_created'])
        reviews = len(context['reviews'])
        
        sandbox_status = "=沙箱模式" if context['use_sandbox'] else "本地模式"
        sandbox_status_en = "Sandbox Mode" if context['use_sandbox'] else "Local Mode"

        if language == 'cn':
            summary = f"""
            代码会话总结：
                总操作数: {total_ops}
                代码生成: {code_generated} 次
                代码执行: {executions} 次
                文件创建: {files_created} 个
                代码审查: {reviews} 次
                {sandbox_status}: {context['sandbox_url'] if context['use_sandbox'] else context['work_dir']}
                主要语言: {context['language']}
            """
        else:
            summary = f"""
            Code Session Summary:
                Total operations: {total_ops}
                Code generations: {code_generated}
                Code executions: {executions}
                Files created: {files_created}
                Code reviews: {reviews}
                {sandbox_status_en}: {context['sandbox_url'] if context['use_sandbox'] else context['work_dir']}
                Primary language: {context['language']}
            """
        
        return summary
