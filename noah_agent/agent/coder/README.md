# 🤖 Simple Coder Agent

一个简单易懂、功能完整的代码智能体，支持代码生成、执行、文件管理和代码审查。

## ✨ 核心特性

### 🔧 可插拔设计
- **灵活配置**：可以选择性启用功能模块
- **最小依赖**：核心功能独立，可单独使用
- **易于扩展**：新功能可以轻松添加

### 🛡️ 安全保障
- **远程沙箱**：代码在完全隔离的远程沙箱中执行
- **API隔离**：通过HTTP API与沙箱通信，无直接系统访问
- **本地备选**：沙箱不可用时自动切换到本地安全模式
- **危险检测**：多层安全检查机制

### 📊 实时反馈
- **search_node**：提供详细的操作进度跟踪
- **状态管理**：实时显示每个操作的状态
- **可视化界面**：支持前端实时显示

## 🏗️ 架构设计

```
SimpleCoderAgent
├── 核心功能 (必需)
│   ├── CodeGeneration    # 代码生成
│   ├── CodeExecution     # 代码执行
│   └── Finished         # 完成标记
├── 可选功能 (可插拔)
│   ├── FileManagement   # 文件管理
│   └── CodeReview       # 代码审查
└── 支持组件
    ├── search_node      # 状态跟踪
    └── MindSearchHelper # 辅助工具
```

## 🚀 快速开始

### 沙箱模式使用（推荐）

```python
from agent.coder.simple_coder_agent import SimpleCoderAgent

# 创建沙箱模式的Coder（最安全）
coder = SimpleCoderAgent(
    enable_file_management=True,
    enable_code_review=True,
    sandbox_url="http://0.0.0.0:8194",  # 沙箱服务地址
    use_sandbox=True                    # 启用沙箱模式
)

# 使用智能体
async for response in coder.use_tool(
    user_prompt="生成一个计算斐波那契数列的函数并执行",
    language="python"
):
    print(response)
```

### 本地模式使用（备选方案）

```python
# 创建本地模式的Coder（沙箱不可用时）
coder = SimpleCoderAgent(
    work_dir="./my_project",
    enable_file_management=True,
    enable_code_review=True,
    use_sandbox=False               # 禁用沙箱，使用本地模式
)

# 复杂任务处理
async for response in coder.use_tool(
    user_prompt="创建一个待办事项管理器，包含增删改查功能",
    language="python",
    ui_language="cn"
):
    # 处理响应...
    pass
```

## 🛠️ 工具详解

### 1. CodeGeneration (代码生成)
**功能**：根据需求描述生成代码
**输入**：
- `task_description`: 功能描述
- `language`: 编程语言 (默认: python)
- `save_to_file`: 保存路径 (可选)

**示例**：
```python
# 生成代码
result = await code_gen.run(
    task_description="创建一个用户认证类",
    language="python"
)
```

### 2. CodeExecution (代码执行)
**功能**：在沙箱环境中安全执行代码
**输入**：
- `code`: 要执行的代码
- `language`: 编程语言 (默认: python)
- `timeout`: 超时时间 (默认: 30秒)

**沙箱特性**：
- 远程沙箱执行，完全隔离
- 支持多种编程语言
- 自动超时保护
- 无系统访问权限

**支持的语言**：
- python3: Python代码
- node: JavaScript代码
- java: Java代码
- go: Go代码
- cpp: C++代码
- c: C代码

### 3. FileManagement (文件管理) - 可选
**功能**：管理沙箱或本地工作目录中的文件
**操作类型**：
- `read`: 读取文件
- `write`: 写入文件
- `create`: 创建文件
- `list`: 列出目录

**沙箱模式**：
- 通过沙箱API进行文件操作
- 完全隔离的文件系统
- 无主机文件系统访问

**本地模式**：
- 路径遍历防护
- 工作目录限制
- 权限检查

### 4. CodeReview (代码审查) - 可选
**功能**：对代码进行质量审查
**审查类型**：
- `basic`: 基础审查 (结构、长度等)
- `security`: 安全审查 (漏洞检测)
- `style`: 风格审查 (编码规范)

## 📊 search_node 状态跟踪

### 节点类型
- **根节点**：用户请求的主要信息
- **思考节点**：AI的分析过程
- **工具节点**：具体操作的执行状态
- **总结节点**：会话结果总结

### 状态类型
```python
ProcessingType.THINKING    # 🤔 思考中
ProcessingType.PROCESSING  # 🔄 处理中
ProcessingType.DONE        # ✅ 完成
ProcessingType.RESPONSING  # 💬 响应中
```

### 使用示例
```python
async for response in coder.use_tool(user_prompt="..."):
    if hasattr(response, 'search_graph'):
        root = response.search_graph
        print(f"主任务: {root.query}")
        
        for child in root.children:
            status = "✅" if child.processing_type == 3 else "🔄"
            print(f"  {status} {child.query}: {child.summary}")
```

## ⚙️ 配置选项

### 初始化参数
```python
SimpleCoderAgent(
    work_dir="./workspace",           # 本地工作目录（备选）
    enable_file_management=True,      # 启用文件管理
    enable_code_review=True,          # 启用代码审查
    sandbox_url="http://0.0.0.0:8194", # 沙箱服务地址
    use_sandbox=True,                 # 是否使用沙箱模式
    llm=GPT4o,                       # LLM模型
    sys_prompt="自定义系统提示词"      # 系统提示词
)
```

### 沙箱服务配置
```bash
# 沙箱服务要求
# 1. 代码执行API: POST /v1/sandbox/run
# 2. 请求格式: {"language": "python3", "code": "print('hello')"}
# 3. 响应格式: {"output": "hello\n", "error": null}

# 可选的文件操作API: POST /v1/sandbox/file
# 请求格式: {"operation": "read", "file_path": "test.py", "content": ""}
```

### 运行时参数
```python
coder.use_tool(
    user_prompt="用户请求",
    language="python",               # 代码语言
    ui_language="cn",               # 界面语言
    history_messages=[],            # 历史消息
    **kwargs                        # 其他参数
)
```

## 🔒 安全机制

### 代码执行安全
1. **静态检查**：扫描危险操作模式
2. **沙箱环境**：临时文件执行
3. **资源限制**：CPU、内存、时间限制
4. **网络隔离**：禁止网络访问

### 文件操作安全
1. **路径验证**：防止路径遍历攻击
2. **目录限制**：只能访问工作目录
3. **权限检查**：验证操作权限
4. **类型检查**：验证文件类型

### 危险操作检测
```python
# 被阻止的操作示例
dangerous_patterns = [
    r'import\s+os\s*;.*os\.(system|remove)',  # 系统操作
    r'import\s+subprocess',                   # 子进程
    r'eval\s*\(',                            # 动态执行
    r'exec\s*\(',                            # 动态执行
    r'__import__',                           # 动态导入
]
```

## 🧪 测试和调试

### 运行测试
```bash
# 测试沙箱连接
python test_sandbox_connection.py

# 运行完整测试套件
python test_simple_coder.py

# 运行特定测试
python -c "
import asyncio
from test_simple_coder import test_pluggable_design
asyncio.run(test_pluggable_design())
"
```

### 调试技巧
1. **启用详细日志**：查看详细的执行过程
2. **检查search_node**：跟踪操作状态
3. **验证工具配置**：确认工具列表正确
4. **测试安全机制**：验证安全检查有效

## 📈 性能优化

### 最佳实践
1. **按需启用功能**：只启用必要的工具
2. **合理设置超时**：避免长时间等待
3. **定期清理**：清理临时文件和缓存
4. **监控资源**：关注CPU和内存使用

### 配置建议
```python
# 轻量级配置 - 适合简单任务
light_coder = SimpleCoderAgent(
    enable_file_management=False,
    enable_code_review=False
)

# 完整配置 - 适合复杂项目
full_coder = SimpleCoderAgent(
    work_dir="./project",
    enable_file_management=True,
    enable_code_review=True
)
```

## 🤝 扩展开发

### 添加新工具
1. 继承 `BaseTool` 类
2. 实现 `run` 方法
3. 定义输入schema
4. 添加到工具列表

### 自定义Agent
1. 继承 `SimpleCoderAgent`
2. 重写 `use_tool` 方法
3. 添加自定义逻辑
4. 配置专用工具

## 📝 更新日志

### v1.0.0
- ✅ 基础代码生成和执行功能
- ✅ 可插拔工具设计
- ✅ 安全执行环境
- ✅ search_node状态跟踪
- ✅ 文件管理功能
- ✅ 代码审查功能
- ✅ 完整的测试套件
- ✅ 详细的文档说明

## 📞 支持和反馈

如有问题或建议，请：
1. 查看测试文件了解使用方法
2. 检查安全机制是否正常工作
3. 验证工具配置是否正确
4. 提供详细的错误信息和复现步骤
