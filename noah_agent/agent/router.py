from agent.example_agent.example_agent import WeatherAgent, ChatAgent
from agent.explore.mindsearch_workflow_agent import MindSearchWorkflowAgent
from agent.explore.mindsearch_refer_agent import MindSearchReferAgent
from agent.explore.mindsearch_clinical_guidance_agent import MindSearchClinicalGuideline
from agent.explore.mindsearch_pubmed_agent_v2 import MindSearchPubMedAgentV2
from agent.explore.mindsearch_rewrite_agent import MindSearchRewriteAgent
from agent.explore.mindsearch_agent_v2 import MindSearchAgentV2, MindSearchFinanceHitlAgent
from agent.explore.mindsearch_workflow_agent_v2 import DrugCompetitionHitlAgent, CatalystEventHitlAgent, ClinicalResultsHitlAgent
from agent.catalyst.follow_up import CatalystFollowUpAgent
from agent.toolbox.email_agent import EmailGeneraterAgent
from agent.investment.report import InvestmentReportAgent
from agent.workflow.title_gen import WorkflowTitleGenAgent
from agent.catalyst.technical_analysis import TechnicalAnalysisAgent
from agent.human_in_loop.planning_v2 import PlanningAgent
from agent.synopsis.report_gen import SynopsisAgent
from agent.synopsis.report_gen_v2 import SynopsisAgentV2
from agent.explore.mindsearch_rewrite_agent_v2 import MindSearchRewriteAgentV2

agent_routing = {
    "weather":  WeatherAgent,
    "chat": ChatAgent,
    "technical_analysis": TechnicalAnalysisAgent,
    "catalyst_follow_up": CatalystFollowUpAgent,
    "investment_report": InvestmentReportAgent,
    "synopsis": SynopsisAgent,
    "synopsis_v2": SynopsisAgentV2,
    "workflow_title_gen": WorkflowTitleGenAgent,
    "planning": PlanningAgent,
    "email": EmailGeneraterAgent,
    "mindsearch": MindSearchAgentV2,
    "mindsearchofficialsite": MindSearchAgentV2,
    "mindsearchrefer": MindSearchReferAgent,
    "mindsearchworkflowrefer": MindSearchWorkflowAgent,
    "mindsearchclinicalguideline": MindSearchClinicalGuideline,
    "mindsearchpubmed": MindSearchPubMedAgentV2,
    "mindsearchrewrite": MindSearchRewriteAgentV2,
    "mindsearchfinance": MindSearchFinanceHitlAgent,
    "mindsearchdrugcompetition": DrugCompetitionHitlAgent,
    "mindsearchclinicalresults": ClinicalResultsHitlAgent,
    "mindsearchcatalystevent": CatalystEventHitlAgent,
}
