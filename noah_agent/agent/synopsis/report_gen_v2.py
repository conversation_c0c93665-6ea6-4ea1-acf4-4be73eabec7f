import re
import pytz
from datetime import datetime
import json
import shutil
import os
import asyncio
import io
import logging
import time
import traceback
from typing import Any, Callable, List, Type

from agent.core.preset import AgentPreset
from llm.azure_models import GPT4o
from llm.base_model import BaseLLM
from agent.explore.schema import ProcessingType, SearchNode, SearchType, WebSearchLink, WebSearchSubject
from agent.explore.helper import MindSearchHelper
from agent.synopsis.synopsis_analyzer_v2 import SynopsisAnalyzerV2
from utils.core.exception import UnexpectedException
from agent.synopsis.prompts.report_en import partial_prompt_template_post_en, partial_prompt_template_chain_en, partial_prompt_template_en
from agent.synopsis.prompts.report import partial_prompt_template_cn, partial_prompt_template_post_cn, partial_prompt_template_chain_cn
from llm.deepseek_models import CompositeDeepseekChat, CompositeDeepseekReasoner
from llm.gcp_models import ClaudeSonnet37
from agent.human_in_loop.utils import convert_md_to_docx
from utils.sql_client import get_connection_user, text

logger = logging.getLogger(__name__)


class SynopsisAgentV2(AgentPreset):
    analyzer_class: Type[SynopsisAnalyzerV2] = SynopsisAnalyzerV2
    llm: BaseLLM = GPT4o
    sys_prompt: str = ""
    mindsearch_helper: MindSearchHelper = MindSearchHelper()
    language: str = "cn"
    test: bool = False
    synopsis_analyzer: SynopsisAnalyzerV2 = None
    query_mode: bool = False
    oneshot: bool = False
    
    def __init__(self, query_params, query_mode=False, oneshot=False, gemini_mode=False, **kwargs):
        from bio_quant.models.composite_model import CompositeModel
        from llm.gcp_models import Gemini20Flash, Gemini15Flash
        super().__init__()
        try:
            if 'params' in kwargs and 'language' in kwargs['params']:
                lang = kwargs['params']['language'].lower()
                if lang in ['cn', 'zh']:
                    lang = 'cn'
                self.language = lang
            if 'language' in query_params:
                self.language = query_params.pop('language', 'en').lower()
        except: pass
        if 'test' in kwargs and type(kwargs['test']) == bool:
            self.test = kwargs.pop('test',False)
  
        output_dir = f"outputs/synopsis_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.query_mode = query_mode
        self.oneshot = oneshot
        
        self.synopsis_analyzer = self.analyzer_class(model=Gemini20Flash() if gemini_mode else CompositeModel(), query_params=query_params, language=self.language)
        self.synopsis_analyzer.set_output_dir(output_dir)
        self.synopsis_analyzer.gemini_mode = gemini_mode

    async def run_func(self, func: Callable, buffer: io.StringIO):
        async_generator = func()
        try:
            async for item in async_generator:
                buffer.write(item)
        except Exception as e:
            logger.error(f"run_func failed: {str(e)}")
            raise e
                
        
    def init_search_graph(self, query_mode=False):
        root = SearchNode(search_type=SearchType.UNKNOWN,
                    query="Synopsis generation",
                    key_word="")
        subject = WebSearchSubject.UNKNOWN.value
        root.subject = WebSearchSubject(subject)
        if query_mode:
            return root
        root.thought_process = "报告生成将经过三个步骤" if self.language == 'cn' else "Synopsis generation follows a 3-step process"
        
        steps = ["Obtain clinical data (~5s)",
                "Synopsis generation (2-4 mins)",
                "Proofread synopsis (2-4 mins)"]
        steps_chinese = ["获取临床数据 (~5s)",
                        "综述生成 (2-4分钟)",
                        "综述校对 (2-4分钟)"]
        
        for subtitle in (steps_chinese if self.language == "cn" else steps):
            
            node = SearchNode(search_type=SearchType.UNKNOWN,
                    query=subtitle,
                    key_word="")
            root.add_child(node)
        
        return root
    
    async def _task_with_heartbeat(self, func: Callable, buffer: io.StringIO = None, interval: float = 0.3, stream_status={}, **kwargs):
        r"""
        Since fetch web page contents may cost very long time. Send heartbeat at the same time to avoid connection close.
        """
        try:
            start_time = time.time()
            async def write_buffer():
                f = func(test=self.test, stream_status=stream_status, **kwargs)
                if asyncio.iscoroutine(f):
                    await f
                    return
                async for item in f:
                    if not buffer or not item:
                        continue
                    buffer.write(item)
            task = asyncio.create_task(write_buffer())
            shielded = asyncio.shield(task)

            while not task.done():
                yield None
                await asyncio.sleep(interval)
            
            await shielded
            end_time = time.time()
            logger.info(f"[_task_with_heartbeat]{callable} cost time total {end_time - start_time}s")
            yield None
        except Exception as e:
            traceback.print_exc()
            raise Exception(f"Task {func.__name__} with heartbeat failed: {str(e)}")
        
    async def use_tool(self, user_prompt: str = "", **kwargs):
        from utils.obs.client import upload_file
        translation_task = asyncio.create_task(self.synopsis_analyzer.translate_indication())
        try:
            if self.oneshot:
                sa: SynopsisAnalyzerV2 = self.synopsis_analyzer
                response = self.mindsearch_helper.init_response(self)
                yield response
                try:
                    sa.run_search_trials()
                except Exception as e:
                    # response.search_graph.summary = "DONE"
                    response.content = str(e)
                    yield response
                    return
                response.search_graph = self.init_search_graph(oneshot=True)
                yield response
                response.search_graph.children[0].summary = "DONE"
                response.search_graph.children[0].processing_type = ProcessingType.DONE
                yield response
                trial_data = sa.trial_data
                outcome_data_fields = ['outcomesModule','designModule','statusModule','armsInterventionsModule','design', 'safety','efficacy']
                shared_data_fields = ['identificationModule']
                
                outcome_data = [{k:trial[k] for k in shared_data_fields+outcome_data_fields if k in trial} for trial in trial_data]
                
                for i in range(len(trial_data),0,-1):
                    if len(str(outcome_data[:i])) < 200000:
                        outcome_data = outcome_data[:i]
                        break
                data_dir = os.path.join(sa.output_dir, "data")
                os.makedirs(data_dir, exist_ok=True)
                trial_data_file = os.path.join(data_dir, f'synopsis_partial_data_selected.json')
                with open(trial_data_file, 'w', encoding='utf-8') as f:
                    json.dump(outcome_data, f, indent=4, ensure_ascii=False)
                print(f"- 临床实验数据已保存至：{trial_data_file}")
                
                prompt_template = None
                llm = 'r1'
                with get_connection_user() as conn:
                    synopsis_description_prompt = conn.execute(text(f"""SELECT content, llm, temp FROM "PromptPlayground_prompt" WHERE name='synopsis_test_prompt' ORDER BY revision DESC LIMIT 1"""), {})
                    row = synopsis_description_prompt.fetchone()
                    if not row:
                        raise(Exception('no description prompt found'))
                    prompt_template = row[0]
                    llm = row[1]
                    temp = row[2]
                    if llm == 'claude37':
                        sa.model = ClaudeSonnet37()
                    elif llm == 'v3':
                        sa.model = CompositeDeepseekChat()
                data_dict = {"query_params": sa.query_params, "trial_data": outcome_data}
                extra_args = {"check":False}
                async for _ in self._task_with_heartbeat(sa.build_synopsis_part, prompt_template=prompt_template, data_dict=data_dict, response=response, idx=10, temperature=temp, **extra_args):
                    yield response
                await self.compress_and_upload(sa, response, **kwargs)
                response.content += f'\n\n使用的模型：{llm}'
                response.search_graph.children[-1].summary = "DONE"
                response.search_graph.children[-1].processing_type = ProcessingType.DONE
                response.search_graph.summary = "DONE"
                yield response
                return
            if self.query_mode:
                response = self.mindsearch_helper.init_response(self)
                yield response
                response.search_graph = self.init_search_graph(query_mode=True)
                response.processing_type = ProcessingType.PROCESSING
                
                yield response
                await asyncio.wait([translation_task])
                sa = self.synopsis_analyzer
                if sa.chinese_query_params:
                    response.search_graph.children[0].thought_process += f"适应症译为: {sa.query_params['indication']}\n"
                yield response
                try:
                    sa.run_search_trials()
                except Exception as e:
                    traceback.print_exc()
                    response.content = str(e)
                    response.search_graph.summary = "DONE"
                    response.search_graph.children[0].processing_type = ProcessingType.DONE
                    yield response
                    return
                response.search_graph.children[0].processing_type = ProcessingType.DONE
                
                yield response
                sa.save_trial_data()
                bucket_name = "noahai-userdata-test"
                user = kwargs.get("user", "unknown")
                file_name = f"{sa.output_dir}.json"
                file_path = os.path.join(sa.output_dir, 'data', 'synopsis_trial_data.json')
                for _ in range(3):
                    res = upload_file(bucket_name=bucket_name, object_key=f"synopsis/{user}/{file_name}", file_path=file_path)
                    if res: 
                        logger.info(f"File {file_path} uploaded successfully")
                        response.search_graph.attachments_key = f"synopsis/{user}/{file_name}"
                        break
                    await asyncio.sleep(3)
                else:
                    logger.error(f"Failed to upload {file_path}")
                response.content = "## 下载链接：[临床实验数据]" if self.language == 'cn' else "## Download link: [Trial Data]"
                response.content += f"(https://{bucket_name}.obs.cn-south-1.myhuaweicloud.com/{response.search_graph.attachments_key})" if response.search_graph.attachments_key else ""
                # response.content += f"\n\n```bucketdownload {response.search_graph.attachments_key}```" if response.search_graph.attachments_key else ""
                response.search_graph.summary = "DONE"
                yield response
                return
            # check whether need query
            start_time = time.time()
            # query rewrite
            response = self.mindsearch_helper.init_response(self)
            yield response
            response.search_graph = self.init_search_graph()
            response.processing_type = ProcessingType.PROCESSING
            yield response
            
            await asyncio.wait([translation_task])
            sa = self.synopsis_analyzer
            if sa.chinese_query_params:
                response.search_graph.children[0].thought_process += f"适应症译为: {sa.query_params['indication']}\n"
            yield response
            try:
                sa.run_search_trials()
            except Exception as e:
                traceback.print_exc()
                response.content = str(e)
                response.search_graph = None
                yield response
                return
                
            a,b,c,d = sa.read_prompts_from_user_db()
            version = '3.0'
            if not a: response.search_graph.children[0].thought_process += f"Prompt Description not found, using version {version}\n"
            else: response.search_graph.children[0].thought_process += f"Prompt Description found\n"
            if not b: response.search_graph.children[0].thought_process += f"Prompt Eligibility not found, using version {version}\n"
            else: response.search_graph.children[0].thought_process += f"Prompt Eligibility found\n"
            if not c: response.search_graph.children[0].thought_process += f"Prompt Outcomes not found, using version {version}\n"
            else: response.search_graph.children[0].thought_process += f"Prompt Outcomes found\n"
            if not d: response.search_graph.children[0].thought_process += f"Prompt Posterior not found, using version {version}\n"
            else: response.search_graph.children[0].thought_process += f"Prompt Posterior found\n"

            response.search_graph.children[0].summary = "DONE"
            response.search_graph.children[0].processing_type = ProcessingType.DONE
            yield response
            
            buffer = io.StringIO()
            sa.model = CompositeDeepseekChat()
            await sa.build_trial_data()
            data_dict = {"query_params": sa.query_params, "trial_data": sa.outcome_data, 'other_params': sa.other_params,
            'current_date': datetime.now(pytz.timezone('US/Eastern')).strftime('%Y-%m-%d'), 'synopsis_output_template': sa.synopsis_template_parts[2]}
            partial_prompt_template = partial_prompt_template_cn if self.language == 'cn' else partial_prompt_template_en
            async for _ in self._task_with_heartbeat(sa.build_synopsis_part, buffer=buffer, prompt_template=partial_prompt_template, data_dict=data_dict, response=response, idx=1, check=False, model=ClaudeSonnet37(), temperature=0.05):
                yield response  
                
            data_dict.update({"trial_data": sa.description_data, 'synopsis_output_template': sa.synopsis_template_parts[0]})
            partial_prompt_template_chain = partial_prompt_template_chain_cn if self.language == 'cn' else partial_prompt_template_chain_en
            async for _ in self._task_with_heartbeat(sa.build_synopsis_part, buffer=buffer, prompt_template=partial_prompt_template_chain, data_dict=data_dict, response=response, idx=2, check=True, model=CompositeDeepseekReasoner()):
                yield response  
                
            data_dict.update({"trial_data": sa.eligibility_data, 'synopsis_output_template': sa.synopsis_template_parts[1]})
            async for _ in self._task_with_heartbeat(sa.build_synopsis_part, buffer=buffer, prompt_template=partial_prompt_template_chain, data_dict=data_dict, response=response, idx=3, check=True, model=CompositeDeepseekReasoner()):
                yield response  
            
            logger.info(f"Mindesearch final response input: {kwargs}")
            buffer.seek(0)
            buffer.truncate(0)
            # stream_status = {"enabled": True}
            data_dict['synopsis_output_template'] = sa.synopsis_template_parts[3]
            partial_prompt_template_post = partial_prompt_template_post_cn if self.language == 'cn' else partial_prompt_template_post_en
            async for _ in self._task_with_heartbeat(sa.build_synopsis_part, buffer=buffer, prompt_template=partial_prompt_template_post, data_dict=data_dict, response=response, idx=4, check=False, model=CompositeDeepseekChat()):
                yield response  
            response.search_graph.children[4].summary = "DONE"
            response.search_graph.children[4].processing_type = ProcessingType.DONE
            buffer.seek(0)
            buffer.truncate(0)
            retry = 0
            longest = ""
            
            # Replace consecutive spaces of length 3 or more with empty string
            cleaned_text = re.sub(r' {3,}', '', str(sa.synopsis_parts))
            parts_cleaned_len = len(cleaned_text) - cleaned_text.count(r'\n')
            
            while len(longest) < parts_cleaned_len*0.9 and retry < 3:
                async for _ in self._task_with_heartbeat(sa.synopsis_gen_stream, buffer=buffer, model=CompositeDeepseekChat(), temperature=0.05):
                    s = buffer.getvalue()
                    # response.search_graph.children[4].thought_process = s
                    response.content = s
                    yield response  
                print("len(s):", len(s))
                print("len(sa.synopsis_parts):", len(str(sa.synopsis_parts)))
                if len(s)>len(longest):
                    longest = s
                retry += 1
                buffer.seek(0)
                buffer.truncate(0)
            response.content = longest
            yield response
            buffer.close()
            response.search_graph.children[5].summary = "DONE"
            response.search_graph.children[5].processing_type = ProcessingType.DONE
            # response.content += "\n\n\n"
            # response.content += "## 综述下载链接将在校对后提供，请稍候" if self.language == "cn" else "## Download link: will be ready after proofreading. Please wait for a moment."
            # buffer.seek(0)
            # buffer.truncate(0)
            
            # response.processing_type = ProcessingType.RESPONSING
            # stream_status = {"enabled": True}
            # async for _ in self._task_with_heartbeat(sa.check_hallucination, buffer=buffer, interval=1, stream_status=stream_status):
            #     response.search_graph.children[5].thought_process = buffer.getvalue()
            #     yield response
            # buffer.close()
            # response.search_graph.children[5].summary = "DONE"
            # yield response
            
            # logger.info(f"MindSearch final output: time passed {time.time() - start_time}s")
            
            # Save report and outputs to zip file
            zip_path = f"{sa.output_dir}.zip"
            try:
                convert_md_to_docx(os.path.join(sa.output_dir, "data"))
            except Exception as e:
                logger.info(f"Failed to convert markdown to docx: {str(e)}")
            if not os.path.exists(sa.output_dir + '/data'):
                os.makedirs(sa.output_dir + '/data', exist_ok=True)
            shutil.make_archive(sa.output_dir, 'zip', sa.output_dir)
            logger.info(f"Output saved to {zip_path}")
            
            bucket_name = "noahai-userdata-test"
            user = kwargs.get("user", "unknown")
            file_name = sa.output_dir + ".zip"
            for _ in range(3):
                res = upload_file(bucket_name=bucket_name, object_key=f"synopsis/{user}/{file_name}", file_path=zip_path)
                if res: 
                    logger.info(f"File {zip_path} uploaded successfully")
                    response.search_graph.attachments_key = f"synopsis/{user}/{file_name}"
                    break
                await asyncio.sleep(3)
            else:
                logger.error(f"Failed to upload {zip_path}")
                
            # if hasattr(sa, "synopsis") and sa.synopsis:
            # response.content = sa.synopsis
            response.content = response.content.replace('```markdown', '').replace('```', '')
            response.content += "\n---\n\n"
            response.content += ("## 下载链接：[综述与数据]" if self.language == 'cn' else "## Download link: [Synopsis & Data]") + f"(https://{bucket_name}.obs.cn-south-1.myhuaweicloud.com/{response.search_graph.attachments_key})"
            response.search_graph.summary = "DONE"
            yield response
            
        except Exception as e:
            traceback.print_exc()
            raise UnexpectedException(str(e))
        
    def init_search_graph(self, query_mode=False, oneshot=False):
        root = SearchNode(search_type=SearchType.UNKNOWN,
                    query="Synopsis generation",
                    key_word="")
        subject = WebSearchSubject.UNKNOWN.value
        root.subject = WebSearchSubject(subject)
        if query_mode:
            root.thought_process = "报告生成将经过以下步骤"
            steps = ["获取临床数据 (~5s)"] if self.language == 'cn' else ["Obtain clinical data (~5s)"]
            for subtitle in steps:
                node = SearchNode(search_type=SearchType.UNKNOWN,
                        query=subtitle,
                        key_word="")
                root.add_child(node)
            return root
        if oneshot:
            root.thought_process = "报告生成即将执行"
            steps = ["获取临床数据 (~5s)", "部分综述生成（2-4分钟）"] if self.language == 'cn' else ["Obtain clinical data (~5s)", "Synopsis partial generation (2-4 mins)"]
            for subtitle in steps:
                node = SearchNode(search_type=SearchType.UNKNOWN,
                        query=subtitle,
                        key_word="")
                root.add_child(node)
            return root
        root.thought_process = "报告生成即将执行" if self.language == 'cn' else "Synopsis generation will commence shortly"
        
        steps = ["Obtain clinical data (~5s)",
                 "Synopsis outcomes section generation & verification (3-5 mins)",
                 "Synopsis description section generation & verification (3-5 mins)",
                 "Synopsis eligibility section generation & verification (3-5 mins)",
                 "Synopsis discontinuation section generation & verification (3-5 mins)",
                 "Synopsis generation (2-4 mins)"
                 ]
        steps_chinese = ["获取临床数据 (~5s)",
                         "生成outcomes板块并验证 (3-5分钟)",
                         "生成description板块并验证 (3-5分钟)",
                         "生成eligibility板块并验证 (3-5分钟)",
                         "生成discontinuation板块并验证 (3-5分钟)",
                         "综述生成 (2-4分钟)"]
        
        for subtitle in (steps_chinese if self.language == "cn" else steps):
            
            node = SearchNode(search_type=SearchType.UNKNOWN,
                    query=subtitle,
                    key_word="")
            root.add_child(node)
        
        return root
    
    async def compress_and_upload(self, sa, response, **kwargs):
        from utils.obs.client import upload_file
        
        zip_path = f"{sa.output_dir}.zip"
        if not os.path.exists(sa.output_dir + '/data'):
            os.makedirs(sa.output_dir + '/data', exist_ok=True)
        shutil.make_archive(sa.output_dir, 'zip', sa.output_dir)
        logger.info(f"Output saved to {zip_path}")
        
        bucket_name = "noahai-userdata-test"
        user = kwargs.get("user", "unknown")
        file_name = sa.output_dir + ".zip"
        for _ in range(3):
            res = upload_file(bucket_name=bucket_name, object_key=f"synopsis/{user}/{file_name}", file_path=zip_path)
            if res: 
                logger.info(f"File {zip_path} uploaded successfully")
                response.search_graph.attachments_key = f"synopsis/{user}/{file_name}"
                break
            await asyncio.sleep(3)
        else:
            logger.error(f"Failed to upload {zip_path}")
            
        response.content = response.content.replace('```markdown', '').replace('```', '')
        response.content += "\n---\n\n"
        response.content += ("## 下载链接：[综述与数据]" if self.language == 'cn' else "## Download link: [Synopsis & Data]") + f"(https://{bucket_name}.obs.cn-south-1.myhuaweicloud.com/{response.search_graph.attachments_key})"
