#!/usr/bin/env python3
"""
测试MindSearchFinanceHitlAgent是否返回包含code_graph的响应
"""
import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_finance_agent_response():
    """测试MindSearchFinanceHitlAgent的响应类型"""
    
    print("🧪 测试MindSearchFinanceHitlAgent响应类型")
    print("=" * 80)
    
    try:
        from agent.explore.mindsearch_agent_v2 import MindSearchFinanceHitlAgent
        
        # 创建agent实例
        agent = MindSearchFinanceHitlAgent()
        print(f"✅ Agent创建成功: {type(agent)}")
        print(f"📊 Final Output Agent: {type(agent.final_output_agent)}")
        print(f"📊 Has coder_agent: {hasattr(agent.final_output_agent, 'coder_agent')}")
        
        # 测试init_response
        print("\n🔍 测试Helper.init_response():")
        response = agent.helper.init_response(agent)
        print(f"   响应类型: {type(response)}")
        print(f"   响应字段: {list(response.__dict__.keys())}")
        
        # 检查关键字段
        has_search_graph = hasattr(response, 'search_graph')
        has_code_graph = hasattr(response, 'code_graph')
        
        print(f"   ✅ 有search_graph: {has_search_graph}")
        print(f"   ✅ 有code_graph: {has_code_graph}")
        
        if has_search_graph:
            print(f"   📊 search_graph类型: {type(response.search_graph)}")
        if has_code_graph:
            print(f"   📊 code_graph类型: {type(response.code_graph)}")
        
        # 测试实际的use_tool调用
        print("\n🚀 测试实际use_tool调用:")
        print("   问题: 计算1+1等于几")
        
        response_count = 0
        found_search_graph = False
        found_code_graph = False
        
        async for chunk in agent.use_tool(
            user_prompt="计算1+1等于几",
            language="CN"
        ):
            response_count += 1
            
            # 检查响应类型和字段
            chunk_type = type(chunk).__name__
            has_search = hasattr(chunk, 'search_graph')
            has_code = hasattr(chunk, 'code_graph')
            
            if has_search:
                found_search_graph = True
            if has_code:
                found_code_graph = True
            
            print(f"   📦 响应#{response_count}: {chunk_type}")
            print(f"      search_graph: {'✅' if has_search else '❌'}")
            print(f"      code_graph: {'✅' if has_code else '❌'}")
            
            # 如果有code_graph，显示详细信息
            if has_code and chunk.code_graph:
                print(f"      code_graph详情:")
                print(f"         类型: {type(chunk.code_graph)}")
                print(f"         code_type: {chunk.code_graph.code_type}")
                print(f"         query: {chunk.code_graph.query}")
                print(f"         children数量: {len(chunk.code_graph.children)}")
            
            # 限制测试数量
            if response_count >= 3:
                print("   ⏹️ 达到测试限制，停止测试")
                break
        
        # 总结结果
        print("\n📊 测试结果总结:")
        print(f"   总响应数: {response_count}")
        print(f"   检测到search_graph: {'✅' if found_search_graph else '❌'}")
        print(f"   检测到code_graph: {'✅' if found_code_graph else '❌'}")
        
        if found_search_graph and found_code_graph:
            print("🎉 成功！MindSearchFinanceHitlAgent现在返回包含code_graph的响应")
            return True
        elif found_search_graph:
            print("⚠️ 部分成功：有search_graph但没有code_graph")
            return False
        else:
            print("❌ 失败：既没有search_graph也没有code_graph")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_schema_classes():
    """测试新的Schema类"""
    
    print("\n🧪 测试Schema类")
    print("=" * 80)
    
    try:
        from agent.explore.schema import MindSearchResponse, CodeResponse, MindSearchCoderResponse
        from agent.explore.schema import SearchNode, CodeNode, ProcessingType, SearchType, CodeType, CodeSubject
        
        # 测试MindSearchResponse
        print("📊 测试MindSearchResponse:")
        mind_response = MindSearchResponse()
        print(f"   类型: {type(mind_response)}")
        print(f"   字段: {list(mind_response.__dict__.keys())}")
        print(f"   有search_graph: {hasattr(mind_response, 'search_graph')}")
        print(f"   有code_graph: {hasattr(mind_response, 'code_graph')}")
        
        # 测试CodeResponse
        print("\n📊 测试CodeResponse:")
        code_response = CodeResponse()
        print(f"   类型: {type(code_response)}")
        print(f"   字段: {list(code_response.__dict__.keys())}")
        print(f"   有search_graph: {hasattr(code_response, 'search_graph')}")
        print(f"   有code_graph: {hasattr(code_response, 'code_graph')}")
        
        # 测试MindSearchCoderResponse
        print("\n📊 测试MindSearchCoderResponse:")
        coder_response = MindSearchCoderResponse()
        print(f"   类型: {type(coder_response)}")
        print(f"   字段: {list(coder_response.__dict__.keys())}")
        print(f"   有search_graph: {hasattr(coder_response, 'search_graph')}")
        print(f"   有code_graph: {hasattr(coder_response, 'code_graph')}")
        
        # 测试序列化
        print("\n🔧 测试序列化:")
        import json
        
        try:
            data = coder_response.model_dump()
            json_str = json.dumps(data, ensure_ascii=False)
            print("   ✅ MindSearchCoderResponse序列化成功")
        except Exception as e:
            print(f"   ❌ 序列化失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Schema测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 开始测试CodeResponse功能")
    
    # 测试Schema类
    schema_success = await test_schema_classes()
    
    # 测试Finance Agent
    if schema_success:
        finance_success = await test_finance_agent_response()
    else:
        print("❌ Schema测试失败，跳过Finance Agent测试")
        finance_success = False
    
    print("\n" + "=" * 80)
    print("📊 最终结果:")
    print(f"   Schema测试: {'✅' if schema_success else '❌'}")
    print(f"   Finance Agent测试: {'✅' if finance_success else '❌'}")
    
    if schema_success and finance_success:
        print("🎉 所有测试通过！MindSearchFinanceHitlAgent现在支持code_graph")
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
    
    return schema_success and finance_success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        sys.exit(1)
