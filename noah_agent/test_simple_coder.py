#!/usr/bin/env python3
"""
简单的Coder智能体测试脚本
用于验证功能是否正常工作
"""
import asyncio
import os
import sys
import tempfile
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agent.coder.simple_coder_agent import SimpleCoderAgent


async def test_basic_functionality():
    """测试基础功能"""
    print("🧪 测试基础功能...")
    
    # 创建临时工作目录
    with tempfile.TemporaryDirectory() as temp_dir:
        coder = SimpleCoderAgent(
            work_dir=temp_dir,
            enable_file_management=True,
            enable_code_review=True
        )
        
        # 测试简单的代码生成请求
        user_request = "生成一个Python函数来计算两个数的最大公约数"
        
        print(f"📝 用户请求: {user_request}")
        print("🔄 开始处理...")
        
        response_count = 0
        async for response in coder.use_tool(
            user_prompt=user_request,
            language="python",
            ui_language="cn"
        ):
            response_count += 1
            
            # 打印search_graph状态
            if hasattr(response, 'search_graph') and response.search_graph:
                root = response.search_graph
                print(f"📋 [{response_count}] 根节点: {root.query}")
                
                for i, child in enumerate(root.children):
                    status_icons = {
                        1: "🔄",  # PROCESSING
                        3: "✅",  # DONE
                        9: "🤔"   # THINKING
                    }
                    icon = status_icons.get(child.processing_type, "❓")
                    print(f"   └─ {icon} {child.query}")
                    
                    if child.summary:
                        print(f"      💬 {child.summary}")
            
            # 打印最终内容
            if hasattr(response, 'content') and response.content:
                print(f"🤖 AI回复: {response.content[:200]}...")
            
            # 检查是否完成
            if hasattr(response, 'processing_type') and response.processing_type == 7:
                print("✅ 任务完成!")
                break
        
        print(f"📊 总共收到 {response_count} 个响应")


async def test_pluggable_design():
    """测试可插拔设计"""
    print("\n🔧 测试可插拔设计...")
    
    # 测试最小配置
    minimal_coder = SimpleCoderAgent(
        enable_file_management=False,
        enable_code_review=False
    )
    print(f"✅ 最小配置Coder - 工具数量: {len(minimal_coder.tools)}")
    
    # 测试完整配置
    full_coder = SimpleCoderAgent(
        enable_file_management=True,
        enable_code_review=True
    )
    print(f"✅ 完整配置Coder - 工具数量: {len(full_coder.tools)}")
    
    # 验证工具列表
    minimal_tool_names = [tool.__name__ for tool in minimal_coder.tools]
    full_tool_names = [tool.__name__ for tool in full_coder.tools]
    
    print(f"📋 最小配置工具: {minimal_tool_names}")
    print(f"📋 完整配置工具: {full_tool_names}")
    
    # 验证可插拔性
    assert 'FileManagement' not in minimal_tool_names
    assert 'CodeReview' not in minimal_tool_names
    assert 'FileManagement' in full_tool_names
    assert 'CodeReview' in full_tool_names
    
    print("✅ 可插拔设计测试通过!")


def test_tool_safety():
    """测试工具安全性"""
    print("\n🔒 测试工具安全性...")
    
    from tools.coder.simple_code_tools import CodeExecution, FileManagement
    
    # 测试代码执行安全检查
    code_exec = CodeExecution()
    
    # 安全代码
    safe_code = "print('Hello World')\nresult = 1 + 1\nprint(result)"
    assert code_exec._is_code_safe(safe_code, "python") == True
    print("✅ 安全代码检查通过")
    
    # 危险代码
    dangerous_codes = [
        "import os; os.system('rm -rf /')",
        "import subprocess; subprocess.run(['ls'], shell=True)",
        "eval('print(1)')",
        "exec('import sys')"
    ]
    
    for dangerous_code in dangerous_codes:
        assert code_exec._is_code_safe(dangerous_code, "python") == False
    print("✅ 危险代码检查通过")
    
    # 测试文件管理安全路径
    with tempfile.TemporaryDirectory() as temp_dir:
        file_mgmt = FileManagement(work_dir=temp_dir)
        
        # 安全路径
        safe_paths = ["test.py", "subdir/test.py", "data/file.txt"]
        for path in safe_paths:
            safe_path = file_mgmt._get_safe_path(path)
            assert safe_path is not None
            assert safe_path.startswith(temp_dir)
        print("✅ 安全路径检查通过")
        
        # 危险路径
        dangerous_paths = ["../test.py", "/etc/passwd", "../../secret.txt"]
        for path in dangerous_paths:
            safe_path = file_mgmt._get_safe_path(path)
            assert safe_path is None
        print("✅ 危险路径检查通过")
    
    print("✅ 所有安全性测试通过!")


async def test_search_node_structure():
    """测试search_node结构"""
    print("\n🌳 测试search_node结构...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        coder = SimpleCoderAgent(work_dir=temp_dir)
        
        # 简单请求
        user_request = "创建一个Hello World程序"
        
        final_response = None
        async for response in coder.use_tool(
            user_prompt=user_request,
            language="python"
        ):
            if hasattr(response, 'search_graph'):
                final_response = response
        
        if final_response and final_response.search_graph:
            root = final_response.search_graph
            print(f"📋 根节点查询: {root.query}")
            print(f"📋 根节点主题: {root.subject}")
            print(f"📋 子节点数量: {len(root.children)}")
            
            # 验证节点结构
            assert root.query == user_request
            assert len(root.children) >= 1  # 至少有思考节点
            
            # 检查节点类型
            node_types = [child.search_type for child in root.children]
            print(f"📋 节点类型: {node_types}")
            
            print("✅ search_node结构测试通过!")
        else:
            print("❌ 未找到search_graph")


async def main():
    """主测试函数"""
    print("🚀 开始测试简单Coder智能体")
    print("=" * 50)
    
    try:
        # 运行各项测试
        test_pluggable_design()
        test_tool_safety()
        await test_search_node_structure()
        
        # 注意：这个测试需要LLM，可能会失败
        print("\n⚠️  以下测试需要LLM支持，可能会失败:")
        try:
            await test_basic_functionality()
        except Exception as e:
            print(f"❌ LLM测试失败（预期）: {e}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 50)
    print("🎉 所有测试完成!")
    return True


if __name__ == "__main__":
    # 运行测试
    success = asyncio.run(main())
    sys.exit(0 if success else 1)


# ================================
# 重要说明
# ================================

"""
🧪 测试说明：

1. 基础功能测试：
   - 测试Agent的基本工作流程
   - 验证search_node的状态管理
   - 检查响应格式和内容

2. 可插拔设计测试：
   - 验证工具的动态配置
   - 确保功能可以选择性启用
   - 检查工具列表的正确性

3. 安全性测试：
   - 验证代码执行的安全检查
   - 测试文件路径的安全验证
   - 确保危险操作被正确阻止

4. 结构测试：
   - 验证search_node的层次结构
   - 检查节点状态的正确转换
   - 确保信息的正确传递

📝 运行说明：

1. 直接运行：python test_simple_coder.py
2. 大部分测试不需要LLM即可运行
3. 涉及LLM的测试可能会失败，这是正常的
4. 所有安全性测试都应该通过

🔧 调试建议：

1. 如果测试失败，检查导入路径
2. 确保临时目录有写入权限
3. 查看详细的错误信息和堆栈跟踪
4. 可以单独运行各个测试函数
"""
