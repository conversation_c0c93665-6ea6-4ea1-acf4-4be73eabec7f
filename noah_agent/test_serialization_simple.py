#!/usr/bin/env python3
"""
简单的序列化测试
"""
import asyncio
import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_simple_query():
    """测试简单查询的序列化"""
    print("🧪 测试简单查询序列化...")
    
    try:
        from main import chat_api
        
        body = {
            "user_prompt": "1+1等于几",
            "agent": "mindsearchfinance",
            "history_messages": [],
            "params": {
                "background": '',
                "language": 'CN',
                "model": '',
                "enable_rag": True,
            }
        }
        
        print("🚀 开始测试...")
        
        # 创建响应迭代器
        res = await chat_api(body)
        
        chunk_count = 0
        serialization_errors = 0
        
        async for chunk in res.body_iterator:
            chunk_count += 1
            print(f"📦 处理第 {chunk_count} 个chunk")
            
            try:
                # 尝试序列化
                if hasattr(chunk, 'model_dump'):
                    data = chunk.model_dump()
                    json_str = json.dumps(data, ensure_ascii=False)
                    print(f"✅ 序列化成功，长度: {len(json_str)}")
                elif isinstance(chunk, (dict, list)):
                    json_str = json.dumps(chunk, ensure_ascii=False)
                    print(f"✅ 序列化成功，长度: {len(json_str)}")
                elif isinstance(chunk, str):
                    print(f"✅ 字符串chunk: {chunk[:100]}...")
                else:
                    print(f"⚠️ 未知类型: {type(chunk)}")
                    
            except Exception as e:
                serialization_errors += 1
                print(f"❌ 序列化失败: {e}")
                print(f"   Chunk类型: {type(chunk)}")
                if hasattr(chunk, 'content'):
                    print(f"   Content类型: {type(chunk.content)}")
                
                # 如果序列化失败，尝试其他方法
                try:
                    str_chunk = str(chunk)
                    print(f"✅ 转换为字符串成功: {str_chunk[:100]}...")
                except Exception as e2:
                    print(f"❌ 转换为字符串也失败: {e2}")
            
            # 限制测试数量，避免无限循环
            if chunk_count >= 20:
                print("⏹️ 达到测试限制，停止测试")
                break
        
        print(f"\n📊 测试结果:")
        print(f"   总chunk数: {chunk_count}")
        print(f"   序列化错误: {serialization_errors}")
        
        if serialization_errors == 0:
            print("🎉 所有chunk都能正确序列化!")
            return True
        else:
            print(f"⚠️ 有 {serialization_errors} 个序列化错误")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 开始简单序列化测试")
    print("=" * 50)
    
    success = await test_simple_query()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 序列化测试通过!")
    else:
        print("❌ 序列化测试失败!")
    
    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        sys.exit(1)
