import elasticsearch
import async<PERSON>
from typing import Optional
from utils.core.singleton import Singleton


class ElasticsearchClientSingleton(Singleton):
    _async_instance: Optional[elasticsearch.AsyncElasticsearch] = None
    _instance: Optional[elasticsearch.Elasticsearch] = None
    _initialized: bool = False
    es_url: str = None
    es_username: str = None
    es_password: str = None

    @classmethod
    def get_client(cls) -> elasticsearch.Elasticsearch:
        if cls._instance is None:
            cls.initialize(cls.es_url, cls.es_username, cls.es_password)
        else:
            pass
        return cls._instance

    @classmethod
    def get_asyncclient(cls) -> elasticsearch.AsyncElasticsearch:
        if cls._async_instance is None:
            cls.initialize(cls.es_url, cls.es_username, cls.es_password)
        else:
            pass
        return cls._async_instance

    @classmethod
    def initialize(cls, es_url, es_username, es_password) -> None:
        cls.es_url = es_url
        cls.es_username = es_username,
        cls.es_password = es_password
        if not cls._initialized:
            cls._async_instance = elasticsearch.AsyncElasticsearch(
                hosts=es_url,
                basic_auth=(es_username, es_password),
                max_retries=10,
                retry_on_timeout=True,
                request_timeout=30
            )
            cls._instance = elasticsearch.Elasticsearch(
                hosts=es_url,
                basic_auth=(es_username, es_password),
                max_retries=10,
                retry_on_timeout=True,
                request_timeout=30
            )
            cls._initialized = True
        else:
            pass
        return

    @classmethod
    def cleanup(cls) -> None:
        if cls._async_instance:
            asyncio.run(cls._async_instance.close())
            cls._async_instance = None
        else:
            pass
        if cls._instance:
            cls._instance.close()
            cls._instance = None
        else:
            pass
        cls._initialized = False
        return

    @classmethod
    def reset(cls) -> None:
        cls.cleanup()
        cls._initialize = False
        return
