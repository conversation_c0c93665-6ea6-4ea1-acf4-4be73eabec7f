import re
import time
import json
import httpx
import asyncio
import hashlib

from enum import Enum
from urllib.parse import urlparse
from typing import Tuple, List
from datetime import datetime, timezone, timedelta
from bs4 import BeautifulSoup

from utils.core.httpx_client import HttpxClientSingleton
from azure.data.tables import TableServiceClient
from azure.core.exceptions import ResourceNotFoundError

from config import api_config

import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CacheResultEnum(Enum):
    UNKNOWN = 0
    TIMEOUT = 1
    FETCHED = 2
    FAILED = 3


class ContentFetcherBase:

    def __init__(self):
        
        self.timeout = httpx.Timeout(
            15.0,
            read=20.0,     # Read timeout
        )

        self.table_service_client = TableServiceClient.from_connection_string(conn_str=api_config.AZURE_BLOB_CONN_STR)
        self.table_client = self.table_service_client.get_table_client(table_name="webpage")

        self.headers = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'zh,en-US;q=0.9,en;q=0.8',
            'cache-control': 'max-age=0',
            'cookie': 'PHPSESSID=1434598fa2c827d8317cfd07a12690d8; _gid=GA1.2.604967175.1731922120; _scor_uid=e0f790ec51844e36833bd77a4a73ef8a; __gsas=ID=48d3e9940af69df0:T=1731922153:RT=1731922153:S=ALNI_MZvQxBWz3qs01VOpC-AgbL99KNZSw; pbjs_sharedId=e6b81549-4440-4f26-b278-847e2f24241c; _curator_id=DE.V1.706645562c.1731922161728; _cm=eyIxIjpmYWxzZSwiMiI6ZmFsc2UsIjMiOmZhbHNlfQ==; _lr_sampling_rate=100; _lr_env_src_ats=false; pbjs_sharedId_cst=VyxHLMwsHQ%3D%3D; bm_ss=ab8e18ef4e; bm_s=YAAQ7jDUFyCwWzOTAQAA0GidQwLg7a9tYEtS5HnTjwvZI/q0kUR/pt260oa6RHPbg33ZW+9I7Z6qcH5Lt8iUWGzUyRIXhBSRwHF9gZN1jxsJyhkAoiXCvSEO01iSKjrf8eSClOdCTs04ki+0B3tw1DT7vbNrccXftK3WQQpWi4QQWVzJ7TgTgYTRYS73rSdhHkPJCGCzht5tiVzCZhk9zgOhulYTZua0iG5J1jjowi9y/dro0VwLdf1cyGprqZAwOkFLVO//sIDgOp7uTLo+EdwdJHOiBEY/hBiwGYCCsurVwQYMSaz3WgX8iq6ywu0dAEuyAKs0afRqAVmWx9kjeX0Y3Q==; bm_so=76BBE50CCAB1B9F1594ED191389CB12A1158EB3EEDDE72173C2964DD0FF5BA30~YAAQ7jDUFyGwWzOTAQAA0GidQwHLnYj2AWYU67KqOqFIsKoRvooemEGGVh82IPEiHPsEhUqZ3IXtqs3BQ3qmVEWvt6EvHaELxF6YwodxnELK4TZwW33Up+RHk1KoHbl0t/v4EVmp78S9lEYwCTJE61Z57b3xjpNxUnpQyOvzdmrPfbiArVBpONd1+g7c8/29wMxkZ/Z3qAAY2BioDDGrNdF6/E7aMfNjwJcNifHdgK2IkHO1BACwB4tNGhpJ6DFbfEhS+jwQSq7mz008e2xAyCPGx/5KLGCOMyyFP5FaBRtWPJfJdAzddz1myFT+6lV+GhshxFeKdc+n9z6TiJyqtbMi9zjWORp1GEYWa5eMhPCzyg+2/ryBW/x+POll7TSmT4t1Xg81DOUFUBxdf47bEb/yyiOMutGik/4QJDHoLRTt+ZWPdeUCyCRVeKON1JDIX1vocn63OP+CBpCCDiU=; __gads=ID=98893d9b3e09f75d:T=1731922153:RT=1732006210:S=ALNI_Mb_3STKX6jvQCHV9pA2yGeiRrcQng; __gpi=UID=00000f980d73b9dc:T=1731922153:RT=1732006210:S=ALNI_MaiZE88SgJOXXk11b7rDOjxgTZgYg; __eoi=ID=ff671c38912eed08:T=1731922153:RT=1732006210:S=AA-AfjZmaXfPXYKBDO3CeZ20PetO; FCNEC=%5B%5B%22AKsRol81F-Al6Ry07LLoB4JVsBadj_i-9Tx1M3P6lFqBO7pVeE9Rvd6g0lSIp2BlZPSYMpLDOaskPub0s4xRRC8Fzs-KUzNA54DfJUJWl5eFpNXRYkHGfQhFR1UCEIzgoXQ4wvlVsmkbZyQBOt3mmz3td4bMaXmEbw%3D%3D%22%5D%5D; bm_lso=76BBE50CCAB1B9F1594ED191389CB12A1158EB3EEDDE72173C2964DD0FF5BA30~YAAQ7jDUFyGwWzOTAQAA0GidQwHLnYj2AWYU67KqOqFIsKoRvooemEGGVh82IPEiHPsEhUqZ3IXtqs3BQ3qmVEWvt6EvHaELxF6YwodxnELK4TZwW33Up+RHk1KoHbl0t/v4EVmp78S9lEYwCTJE61Z57b3xjpNxUnpQyOvzdmrPfbiArVBpONd1+g7c8/29wMxkZ/Z3qAAY2BioDDGrNdF6/E7aMfNjwJcNifHdgK2IkHO1BACwB4tNGhpJ6DFbfEhS+jwQSq7mz008e2xAyCPGx/5KLGCOMyyFP5FaBRtWPJfJdAzddz1myFT+6lV+GhshxFeKdc+n9z6TiJyqtbMi9zjWORp1GEYWa5eMhPCzyg+2/ryBW/x+POll7TSmT4t1Xg81DOUFUBxdf47bEb/yyiOMutGik/4QJDHoLRTt+ZWPdeUCyCRVeKON1JDIX1vocn63OP+CBpCCDiU=^1732006214445; ddc-pvc=4; _ga_NC862DPYNN=GS1.1.1732006215.2.0.1732006215.60.0.0; _ga=GA1.2.394647214.1731922119; _gat_UA-78451-2=1; _clck=5p0138%7C2%7Cfr0%7C0%7C1783; _clsk=mslxpu%7C1732006216480%7C1%7C0%7Cf.clarity.ms%2Fcollect',
            'priority': 'u=0, i',
            'sec-ch-ua': '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'cross-site',
            'sec-fetch-user': '?1',
            'upgrade-insecure-requests': '1',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
            'x-wz-env': 'wanzhi-gray-branch'
        }

    def _urlhash(self, url: str) -> str:
        return hashlib.md5(url.encode('utf-8')).hexdigest()
    
    
    def _get_domain(self, url: str) -> str:
        parsed_url = urlparse(url)
        domain_with_port = parsed_url.netloc
        domain = domain_with_port.split(':')[0]
        return domain

    def _fetch_from_azure_blob(self, url: str) -> Tuple[CacheResultEnum, str]:

        try:
            # TODO add time check for content update
            domain = self._get_domain(url)
            urlhash = self._urlhash(url)

            entity = self.table_client.get_entity(
                row_key=urlhash,
                partition_key=domain,
            )

            if 'webpagecontent' not in entity:
                logger.error(f'Azure blob missing content, partition_key:{domain} row_key: {urlhash}')
                return CacheResultEnum.FAILED, 'Azure blob missing content'
            
            # check timeless
            # entity.metadata
            # {'etag': 'W/"datetime\'2025-03-27T07%3A20%3A06.6738491Z\'"', 'timestamp': TablesEntityDatetime(2025, 3, 27, 7, 20, 6, 673849, tzinfo=datetime.timezone.utc)}
            if hasattr(entity, 'metadata'):
                timestamp = entity.metadata.get('timestamp', None)
                if timestamp:
                    # 计算1个月前的时间戳
                    one_month_ago = datetime.now(timezone.utc) - timedelta(days=30)
                    one_month_ago_timestamp = one_month_ago.timestamp()
                    
                    # 如果entity的时间戳早于1个月前，说明内容过期，需要重新获取
                    if timestamp.timestamp() < one_month_ago_timestamp:
                        logger.info(f"Content for {url} is expired (updated: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}, expired since: {one_month_ago.strftime('%Y-%m-%d %H:%M:%S')})")
                        return CacheResultEnum.TIMEOUT, entity['webpagecontent']
                    
                    logger.info(f"Content for {url} is fresh (updated: {timestamp.strftime('%Y-%m-%d %H:%M:%S')})")
            
            return CacheResultEnum.FETCHED, entity['webpagecontent']

        except ResourceNotFoundError as e:
            #logger.error(f"azure blob don't exist", e)
            return CacheResultEnum.FAILED, "Azure blob don't exist"

        except Exception as e:
            logger.error(f"fetch web content from azure blob failed", e)
            return CacheResultEnum.FAILED, f"{type(e).__name__}: {str(e)}"
        

    def _save_azure_blob(self, url: str, content: str, create_partition_key: bool = False):
        try:
            domain = self._get_domain(url)
            urlhash = self._urlhash(url)

            entity = {
                "PartitionKey": domain,
                "RowKey": urlhash,
                "url": url,
                "webpagecontent": content[:31 * 1024], # azure blob max string size is utf-16 32k
            }
            result = self.table_client.upsert_entity(entity=entity)
            logger.info(f"save to Azure blob url:{url}, result:{result}")
        except Exception as e:
            logger.warn("Azure blob save failed", e)

    async def _fetch(self, url: str) -> str:
        try:
            client = HttpxClientSingleton.get_asynclient()
            response = await client.get(
                url, 
                headers=self.headers, 
                timeout=self.timeout,
                follow_redirects=True,
                auth=None,
            )
            response.raise_for_status()
            html = response.text

            loop = asyncio.get_event_loop()
            text = await loop.run_in_executor(None, lambda: BeautifulSoup(html, 'html.parser').get_text())
            cleaned_text = await loop.run_in_executor(None, lambda: re.sub(r'\n+', '\n', text))
            
            return cleaned_text
        except Exception as e:
            logger.warn(f"Request failed for {url}: {type(e).__name__} - {str(e)}")
            return None
        
    def _parse_content(self, content:str, url: str, type: str) -> str:
        if content is None:
            return None
        
        if 'patent' == type:
            if 'patents.google' in url:
                _, _, wanted = content.partition("Description")
                wanted = wanted.lstrip()
                if wanted != '':
                    return wanted[:1024 * 15]
            elif 'magtech' in url:
                _, _, wanted = content.partition("下一篇")
                wanted = wanted.lstrip()
                if wanted != '':
                    return wanted[:1024 * 15]
        return content

    async def fetch(self, url: str, type: str = 'web') -> Tuple[bool, str]:
        try:
            # Try fetch from Azure table storage
            cache_result, content = self._fetch_from_azure_blob(url=url)
            if CacheResultEnum.FETCHED == cache_result:
                return True, str(content)
            
            result = await self._fetch(url)

            result = self._parse_content(result, url, type)

            if result:
                # save file to azure blob
                self._save_azure_blob(url, result)

                return True, result
            
            elif CacheResultEnum.TIMEOUT == cache_result:
                return True, str(content)

            else:
                return False, 'fetching failed'
            
        except asyncio.TimeoutError:
            logger.warn(f"Timeout firecrawl while fetching {url}")
            return False, "Request timeout"
        except Exception as e:
            logger.warn(f"Error while fetching {url} {str(e)}")
            return False, str(e)


class FirecrawlFetcher(ContentFetcherBase):

    #https://docs.firecrawl.dev/api-reference/endpoint/scrape
    #https://docs.firecrawl.dev/features/fast-scraping

    def __init__(self):
        super().__init__()

    async def _fetch(self, url: str) -> str:
        try:

            payload = {
                "url": url,
                "formats": ["markdown"],
                "onlyMainContent": True,
                "maxAge": 604800000, # 1 week
                "timeout": 25*1000 #ms
            }

            headers = {
                "Authorization": f"Bearer {api_config.FIRECRAWL_API_KEY}",
                "Content-Type": "application/json"
            }

            client = HttpxClientSingleton.get_asynclient()
            response = await client.post(
                url=api_config.FIRECRAWL_BASE_URL,
                json=payload,
                headers=headers,
                timeout=self.timeout,
                auth=None,
            )
            response.raise_for_status()
            result = response.json()

            markdown = None
            if result and result.get('success', False):
                markdown = str(result.get('data', {}).get('markdown', ''))
            
            return markdown

        except Exception as e:
            logger.warn(f"Firecrawl failed to fetch {url}", e)
            return None


class JinaFetcher(ContentFetcherBase):

    def __init__(self):
        self.api_key = api_config.JINA_API_KEY
        self.headers = {
            "Authorization": f" Bearer {self.api_key}",
            "X-Retain-Images": "none",
            "X-Return-Format": "markdown", # cost time too long
            "X-Token-Budget": 250000,
        }
        super().__init__()

    async def _fetch(self, url: str) -> str:
        try:
            client = HttpxClientSingleton.get_asynclient()
            response = await client.get(
                url,
                headers=self.headers,
                timeout=self.timeout,
                auth=None,
            )
            response.raise_for_status()
            content = response.text
            
            return str(content)
        except asyncio.TimeoutError:
            logger.warn(f"Timeout jina while fetching {url}")
            return None
        except Exception as e:
            logger.warn(f"Jina failed to fetch {url} {str(e)}")
            return None
        

class SerperFetch(ContentFetcherBase):

    def __init__(self):
        self.api_key = api_config.GOOGLE_SERPER_API_KEY
        self.base_url = api_config.SERPER_SCRAPE_URL
        self.headers = {
            'X-API-KEY': self.api_key,
            'Content-Type': 'application/json',
        }
        super().__init__()

    async def _fetch(self, url: str) -> str:
        try:
            payload = json.dumps({
                "url": url,
                "includeMarkdown": True
                })
            client = HttpxClientSingleton.get_asynclient()
            response = await client.post(
                self.base_url,
                json=payload,
                headers=self.headers,
                timeout=self.timeout,
                auth=None,
            )
            data = response.read()
            return str(data)
        except asyncio.TimeoutError:
            logger.warn(f"Timeout Seper while fetching {url}")
            return None
        except Exception as e:
            logger.warn(f"Seper failed to fetch {url} {str(e)}")
            return None


class ContentFetcher:

    firecrawl_client = FirecrawlFetcher()
    jina_client = JinaFetcher()
    serper_client = SerperFetch()

    async def _retry(
        self,
        urls: list[str],
        url_map: dict,
        crawler: str = 'firecrawl'):
        
        tasks = []
        urls = [url for url in urls if url not in url_map]
        for url in urls:
            if 'firecrawl' == crawler:
                task = asyncio.create_task(
                    self.jina_client.fetch(url)
                )
            else:
                task = asyncio.create_task(
                    self.firecrawl_client.fetch(url)
                )
            tasks.append((task, url))

        count = 0
        for task, url in tasks:
            try:
                web_success, web_content = await task
                if web_success:
                    count += 1
                    url_map[url] = web_content
            except Exception as exc:
                logger.warn(f"Retry {url} fetch failed and meet exception {str(exc)}")
        
        return url_map


    async def fetch_urls(self, urls: list[str], region: str = 'global', type: str = 'web', enable_retry: bool = False):
        r"""
        So far region only support en and zh, since jina could get China based website, i.e. Xueqiu, Zhihu
        """
        start_time = time.time()

        tasks = []
        crawler = 'firecrawl'
        for url in urls:
            if 'china' == region.lower() or 'patent' == type.lower():
                task = asyncio.create_task(
                    self.jina_client.fetch(url, type.lower())
                )
                crawler = 'jina'
            else:
                task = asyncio.create_task(
                    self.firecrawl_client.fetch(url, type.lower())
                )
            tasks.append((task, url))
        
        res = {}
        for task, url in tasks:
            try:
                web_success, web_content = await task
                if web_success:
                    res[url] = web_content
            except Exception as exc:
                logger.warn(f"{url} fetch failed and meet exception {str(exc)}")

        if enable_retry and len(res) < len(urls):
            res = await self._retry(urls, res, crawler)

        end_time = time.time()
        logger.info(f"Fetch link content cost {end_time - start_time}, fetched page count {len(res)}, total count {len(urls)} for region {region} and type {type}")
        
        return res

    async def fetch_url(self, url: str, region: str = 'global', type: str = 'web'):
        start_time = time.time()

        if 'china' == region.lower() or 'patent' == type.lower():
            result = await self.jina_client.fetch(url)
        else:
            result = await self.firecrawl_client.fetch(url)
            
        end_time = time.time()
        logger.info(f"Fetch link content cost {end_time - start_time}")
        return result

