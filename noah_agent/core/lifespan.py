from fastapi import <PERSON>AP<PERSON>
from contextlib import asynccontextmanager

from llm.azure_models import <PERSON>T4<PERSON>, <PERSON>T41, <PERSON>T4<PERSON><PERSON><PERSON>, <PERSON>To1, <PERSON>To1<PERSON><PERSON>, <PERSON>To3<PERSON><PERSON>, GPTo3, <PERSON>To4<PERSON><PERSON>, GPT4oWorkflow, <PERSON>
from utils.core.httpx_client import HttpxClientSingleton
from utils.core.elasticsearch_client import ElasticsearchClientSingleton
from llm.gcp_models import Gemini<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>37Thinking
from llm.deepseek_models import DeepseekChat, DeepseekChatR1, <PERSON><PERSON><PERSON><PERSON><PERSON>seek<PERSON><PERSON>, <PERSON>oshanDeepseekChatR1, SiliconflowDeepseekChat, SiliconflowDeepseekChatR1, CompositeDeepseekChat, CompositeDeepseekReasoner, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>T<PERSON><PERSON><PERSON>seekChat2, CompositeDeepseekChatPlanning
from config import api_config, settings
import httpx

granular_timeout = httpx.Timeout(45, connect=10.0)
low_timeout_0_retry = {"timeout": granular_timeout, "max_retries": 0}


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Initialize clients
    HttpxClientSingleton.initialize()
    ElasticsearchClientSingleton.initialize(
        es_url=settings.NOAH_ELASTICSEARCH_URL,
        es_username=settings.NOAH_ELASTICSEARCH_USERNAME,
        es_password=settings.NOAH_ELASTICSEARCH_PASSWORD
    )
    GeminiClientSingleton.initialize()

    GPT4o.initialize(
        api_key=api_config.AZURE_GPT4_OPENAI_API_KEY,
        api_version=api_config.AUZRE_GPT4o_MIN_VERSION,
        azure_endpoint=api_config.AZURE_GPT4_AZURE_ENDPOINT
    )
    GPT41.initialize(
        api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPT4_1_VERSION,
        azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )
    GPT4oMini.initialize(
        api_key=api_config.AZURE_GPT4_OPENAI_API_KEY,
        api_version=api_config.AUZRE_GPT4o_MIN_VERSION,
        azure_endpoint=api_config.AZURE_GPT4_AZURE_ENDPOINT
    )
    GPTo1.initialize(
        api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPTo1_VERSION,
        azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )
    GPTo1Mini.initialize(
        api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPTo1_MINI_VERSION,
        azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )
    GPTo3Mini.initialize(
        api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPTo3_MIN_VERSION,
        azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )
    GPTo3.initialize(
        api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPTo3_VERSION,
        azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )
    GPTo4Mini.initialize(
        api_key=api_config.AZURE_GPTo4_MIN_API_KEY,
        api_version=api_config.AZURE_GPTo4_MIN_VERSION,
        azure_endpoint=api_config.AZURE_GPTo4_MIN_ENDPOINT,
        **low_timeout_0_retry
    )
    GPT4oWorkflow.initialize(
        api_key=api_config.AZURE_GPT4_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPT4_OPENAI_API_VERSION,
        azure_endpoint=api_config.AZURE_GPT4_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )

    Ada.initialize(
        api_key=api_config.AZURE_ADA002_OPENAI_API_KEY,
        api_version=api_config.AZURE_ADA002_OPENAI_API_VERSION,
        azure_endpoint=api_config.AZURE_ADA002_AZURE_ENDPOINT,
        azure_deployment=api_config.AZURE_ADA002_AZURE_DEPLOYMENT
    )

    ClaudeSonnet35()
    ClaudeSonnet37()
    ClaudeSonnet4()
    ClaudeOpus4()
    ClaudeSonnet37Thinking()

    DeepseekChat()
    DeepseekChatR1()
    HuoshanDeepseekChatR1()
    HuoshanDeepseekChat()
    SiliconflowDeepseekChatR1()
    SiliconflowDeepseekChat()
    CompositeDeepseekChat()
    CompositeDeepseekReasoner()
    ClaudeThenDeepseekChat()
    ClaudeThenDeepseekChat2()
    CompositeDeepseekChatPlanning()

    yield

    # Cleanup clients
    HttpxClientSingleton.cleanup()

    ElasticsearchClientSingleton.cleanup()

    await GeminiClientSingleton.cleanup()

    await GPT4o.cleanup(
        api_key=api_config.AZURE_GPT4_OPENAI_API_KEY,
        api_version=api_config.AUZRE_GPT4o_MIN_VERSION,
        azure_endpoint=api_config.AZURE_GPT4_AZURE_ENDPOINT
    )
    await GPT41.cleanup(
        api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPT4_1_VERSION,
        azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )
    await GPT4oMini.cleanup(
        api_key=api_config.AZURE_GPT4_OPENAI_API_KEY,
        api_version=api_config.AUZRE_GPT4o_MIN_VERSION,
        azure_endpoint=api_config.AZURE_GPT4_AZURE_ENDPOINT
    )
    await GPTo1.cleanup(
        api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPTo1_VERSION,
        azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )
    await GPTo1Mini.cleanup(
        api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPTo1_MINI_VERSION,
        azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )
    await GPTo3Mini.cleanup(
        api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPTo3_MIN_VERSION,
        azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )
    await GPTo3.cleanup(
        api_key=api_config.AZURE_GPTo1_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPTo3_VERSION,
        azure_endpoint=api_config.AZURE_GPTo1_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )
    await GPTo4Mini.cleanup(
        api_key=api_config.AZURE_GPTo4_MIN_API_KEY,
        api_version=api_config.AZURE_GPTo4_MIN_VERSION,
        azure_endpoint=api_config.AZURE_GPTo4_MIN_ENDPOINT,
        **low_timeout_0_retry
    )
    await GPT4oWorkflow.cleanup(
        api_key=api_config.AZURE_GPT4_OPENAI_API_KEY,
        api_version=api_config.AZURE_GPT4_OPENAI_API_VERSION,
        azure_endpoint=api_config.AZURE_GPT4_AZURE_ENDPOINT,
        **low_timeout_0_retry
    )

    Ada.cleanup(
        api_key=api_config.AZURE_ADA002_OPENAI_API_KEY,
        api_version=api_config.AZURE_ADA002_OPENAI_API_VERSION,
        azure_endpoint=api_config.AZURE_ADA002_AZURE_ENDPOINT,
        azure_deployment=api_config.AZURE_ADA002_AZURE_DEPLOYMENT
    )

    ClaudeSonnet35.cleanup()
    ClaudeSonnet37.cleanup()
    ClaudeSonnet4.cleanup()
    ClaudeOpus4.cleanup()
    ClaudeSonnet37Thinking.cleanup()

    DeepseekChat.cleanup()
    DeepseekChatR1.cleanup()
    HuoshanDeepseekChatR1.cleanup()
    HuoshanDeepseekChat.cleanup()
    SiliconflowDeepseekChatR1.cleanup()
    SiliconflowDeepseekChat.cleanup()
    CompositeDeepseekChat.cleanup()
    CompositeDeepseekReasoner.cleanup()
    ClaudeThenDeepseekChat.cleanup()
    ClaudeThenDeepseekChat2.cleanup()
    CompositeDeepseekChatPlanning.cleanup()
