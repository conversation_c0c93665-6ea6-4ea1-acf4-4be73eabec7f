#!/usr/bin/env python3
"""
测试沙箱连接的简单脚本
用于验证沙箱服务是否可用
"""
import asyncio
import aiohttp
import json
import sys


async def test_sandbox_connection(sandbox_url="http://0.0.0.0:8194"):
    """测试沙箱连接"""
    print(f"🔗 测试沙箱连接: {sandbox_url}")
    
    # 测试代码执行API
    code_endpoint = f"{sandbox_url}/v1/sandbox/run"
    test_code = """
print("Hello from sandbox!")
result = 2 + 3
print(f"2 + 3 = {result}")
"""
    
    payload = {
        "language": "python3",
        "code": test_code
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            print("📤 发送测试代码到沙箱...")
            async with session.post(
                code_endpoint,
                json=payload,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    print("✅ 沙箱代码执行成功!")
                    print(f"📤 输出: {result.get('output', '')}")
                    if result.get('error'):
                        print(f"⚠️  错误: {result.get('error')}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ 沙箱返回错误 {response.status}: {error_text}")
                    return False
                    
    except aiohttp.ClientError as e:
        print(f"❌ 无法连接到沙箱服务: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False


async def test_sandbox_file_operations(sandbox_url="http://0.0.0.0:8194"):
    """测试沙箱文件操作API（如果支持）"""
    print(f"\n📁 测试沙箱文件操作: {sandbox_url}")
    
    file_endpoint = f"{sandbox_url}/v1/sandbox/file"
    
    # 测试创建文件
    create_payload = {
        "operation": "create",
        "file_path": "test.py",
        "content": "print('Hello from file!')\n"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            print("📤 测试文件创建...")
            async with session.post(
                file_endpoint,
                json=create_payload,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    print("✅ 沙箱文件操作成功!")
                    print(f"📤 结果: {result}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"⚠️  沙箱文件API可能不支持 {response.status}: {error_text}")
                    return False
                    
    except aiohttp.ClientError as e:
        print(f"⚠️  文件操作API不可用: {e}")
        return False
    except Exception as e:
        print(f"❌ 文件操作测试异常: {e}")
        return False


async def test_coder_with_sandbox():
    """测试Coder智能体的沙箱功能"""
    print(f"\n🤖 测试Coder智能体沙箱功能...")
    
    try:
        # 导入Coder智能体
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from agent.coder.simple_coder_agent import SimpleCoderAgent
        
        # 创建沙箱模式的Coder
        coder = SimpleCoderAgent(
            enable_file_management=False,  # 先只测试代码执行
            enable_code_review=False,
            use_sandbox=True,
            sandbox_url="http://0.0.0.0:8194"
        )
        
        print("✅ Coder智能体创建成功")
        print(f"🏖️  沙箱URL: {coder.sandbox_url}")
        print(f"🔧 工具数量: {len(coder.tools)}")
        
        # 注意：这里不实际调用use_tool，因为需要LLM
        print("⚠️  完整功能测试需要LLM支持")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入Coder智能体失败: {e}")
        return False
    except Exception as e:
        print(f"❌ Coder测试异常: {e}")
        return False


def print_sandbox_info():
    """打印沙箱信息"""
    print("=" * 60)
    print("🏖️  沙箱服务信息")
    print("=" * 60)
    print("默认地址: http://0.0.0.0:8194")
    print("代码执行API: /v1/sandbox/run")
    print("文件操作API: /v1/sandbox/file (可选)")
    print("")
    print("支持的语言:")
    print("- python3: Python代码执行")
    print("- node: JavaScript代码执行")
    print("- java: Java代码执行")
    print("- go: Go代码执行")
    print("- cpp: C++代码执行")
    print("- c: C代码执行")
    print("")
    print("请求格式:")
    print("POST /v1/sandbox/run")
    print('{"language": "python3", "code": "print(\\"Hello\\")"}')
    print("=" * 60)


async def main():
    """主测试函数"""
    print("🧪 沙箱连接测试工具")
    
    # 打印沙箱信息
    print_sandbox_info()
    
    # 测试沙箱连接
    sandbox_url = "http://0.0.0.0:8194"
    
    print(f"\n🚀 开始测试沙箱服务: {sandbox_url}")
    
    # 测试代码执行
    code_success = await test_sandbox_connection(sandbox_url)
    
    # 测试文件操作（可选）
    file_success = await test_sandbox_file_operations(sandbox_url)
    
    # 测试Coder智能体
    coder_success = await test_coder_with_sandbox()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    print(f"代码执行API: {'✅ 可用' if code_success else '❌ 不可用'}")
    print(f"文件操作API: {'✅ 可用' if file_success else '⚠️  不可用/不支持'}")
    print(f"Coder智能体: {'✅ 正常' if coder_success else '❌ 异常'}")
    
    if code_success:
        print("\n🎉 沙箱服务基本功能正常，可以使用Coder智能体!")
        print("💡 建议:")
        print("   - 使用 use_sandbox=True 启用沙箱模式")
        print("   - 如果文件操作API不可用，可以只使用代码执行功能")
    else:
        print("\n⚠️  沙箱服务不可用，建议:")
        print("   - 检查沙箱服务是否启动")
        print("   - 确认服务地址和端口正确")
        print("   - 使用 use_sandbox=False 切换到本地模式")
    
    return code_success


if __name__ == "__main__":
    # 运行测试
    success = asyncio.run(main())
    sys.exit(0 if success else 1)


# ================================
# 使用说明
# ================================

"""
🔧 使用方法:

1. 直接运行测试:
   python test_sandbox_connection.py

2. 测试自定义沙箱地址:
   修改 sandbox_url 变量

3. 如果沙箱服务不可用:
   - 检查服务是否启动
   - 确认防火墙设置
   - 尝试使用本地模式

📝 沙箱服务要求:

1. 服务必须支持 POST /v1/sandbox/run
2. 请求格式: {"language": "python3", "code": "..."}
3. 响应格式: {"output": "...", "error": "..."}
4. 文件操作API是可选的

🚨 故障排除:

1. 连接超时:
   - 检查网络连接
   - 确认服务地址正确

2. 服务错误:
   - 查看沙箱服务日志
   - 确认API格式正确

3. 权限问题:
   - 检查服务权限设置
   - 确认端口可访问
"""
